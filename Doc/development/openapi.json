{"openapi": "3.0.0", "info": {"title": "dev_service", "description": "development environment server", "version": "2025-08-26T05:17:29.203Z"}, "servers": [{"url": "https://dev-product-api.hawak.io/", "description": "development environment server"}], "tags": [{"name": "Category"}, {"name": "Category > Sort product list"}, {"name": "Category > Dashboard top category"}, {"name": "Configuration"}, {"name": "Configuration > Tax"}, {"name": "Configuration > Master tax"}, {"name": "Datasheet"}, {"name": "Data Sync", "description": "This folder contains APIs used by the tablet app to fetch data for newly added and updated records."}, {"name": "Deals"}, {"name": "Deals > Deal"}, {"name": "Deals > Update Product List"}, {"name": "Deals > Deals Product List"}, {"name": "Deals > Deal Product"}, {"name": "Images"}, {"name": "Master Data"}, {"name": "Master Data > Attribute"}, {"name": "Master Data > Attribute Set"}, {"name": "Master Data > Brand"}, {"name": "Master Data > Unit"}, {"name": "Master Data > Price"}, {"name": "Master Data > Attribute Association"}, {"name": "Order"}, {"name": "Order > Cart"}, {"name": "Order > Stats"}, {"name": "Order > Drafts"}, {"name": "Order > Export"}, {"name": "Order > Orders"}, {"name": "Product"}, {"name": "Product > Product"}, {"name": "Product > Products"}, {"name": "Product > Favorite products"}], "paths": {"/productService/category/allProductList": {"get": {"tags": ["Category > Sort product list"], "summary": "All Category Product list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "familyId", "in": "query", "schema": {"type": "string"}, "description": "family category id", "example": "6375c5be421cf4001212f453"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "incrementLimit", "in": "query", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/category/categoryProductList": {"get": {"tags": ["Category > Sort product list"], "summary": "Category Product list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1001"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}, {"name": "appType", "in": "query", "schema": {"type": "string"}, "example": "NATIVE"}, {"name": "priceListId", "in": "query", "schema": {"type": "string"}, "example": "64156686ea18a9f9411d6664"}, {"name": "familyId", "in": "query", "schema": {"type": "string"}, "example": "64bbd91fd28ff53223dd0b16"}, {"name": "categoryId", "in": "query", "schema": {"type": "string"}, "example": "64bbd940d28ff53223dd0b8a"}, {"name": "branchId", "in": "query", "schema": {"type": "string"}, "example": "63c124e6efa1790013071f93"}, {"name": "isProductSplitting", "in": "query", "schema": {"type": "boolean"}, "description": "Is temporary flag, will be removed soon", "example": "true"}, {"name": "productSchemaType", "in": "query", "schema": {"type": "string"}, "example": "products_2.0"}, {"name": "salesPersonUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "648d99c0cd63200012aab771"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Category > Sort product list"], "summary": "Product sequnce", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1001, "status": "ACTIVE", "appType": "NATIVE", "priceListId": "64156686ea18a9f9411d6664", "categoryId": "64bbd940d28ff53223dd0b8a", "familyId": "64bbd91fd28ff53223dd0b16", "perPage": 20, "page": 1, "salesPersonUserRoleId": "648d99c0cd63200012aab771", "hideOutOfStock": true, "branchId": "63c124e6efa1790013071f93", "productSchemaType": "products_2.0", "isProductSplitting": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/category/dashboard/topCategory": {"get": {"tags": ["Category > Dashboard top category"], "summary": "Top category", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/category": {"post": {"tags": ["Category"], "summary": "Add and Edit category", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"categoryId": "636c8b35e4f5e5558c72f670", "categoryName": "NE<PERSON> family", "secondaryCategoryName": "NEW Secondary lang name", "tenantId": 1035, "type": "FAMILY", "isActive": true, "parentFamilyId": "636bae633faa4a5020118c0f", "parentCategoryId": "636cc289ace2943700645843"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Category"], "summary": "Change category sequnce", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"categoryId": ["636bae633faa4a5020118c0f", "637484cb8dd3c800120bd3cf", "6373668ad35c5b0012db9c19", "637484be8dd3c800120bd3cc", "637366d6d35c5b0012db9c1f", "637484768dd3c800120bd3b9", "6373674dd35c5b0012db9c23", "6374852b8dd3c800120bd3e7", "637484ab8dd3c800120bd3c5", "637484858dd3c800120bd3bd", "637485018dd3c800120bd3d9", "6373676cd35c5b0012db9c27", "637485118dd3c800120bd3e0", "63771137afe939001295298a", "6377676951ecdd00121aa744", "6377681751ecdd00121aa773"], "type": "FAMILY"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Category"], "summary": "Get  category list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1001"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}, {"name": "appType", "in": "query", "schema": {"type": "string"}, "example": "NATIVE"}, {"name": "priceListId", "in": "query", "schema": {"type": "string"}, "example": "64156686ea18a9f9411d6664"}, {"name": "categoryId", "in": "query", "schema": {"type": "string"}, "example": "64bbd940d28ff53223dd0b8a"}, {"name": "familyId", "in": "query", "schema": {"type": "string"}, "example": "64bbd91fd28ff53223dd0b16"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "salesPersonUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "648d99c0cd63200012aab771"}, {"name": "hideOutOfStock", "in": "query", "schema": {"type": "boolean"}, "example": "true"}, {"name": "branchId", "in": "query", "schema": {"type": "string"}, "example": "63c124e6efa1790013071f93,"}, {"name": "productSchemaType", "in": "query", "schema": {"type": "string"}, "example": "products_2.0"}, {"name": "isProductSplitting", "in": "query", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Category"], "summary": "Delete category", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/category/get-category": {"get": {"tags": ["Category"], "summary": "Get category", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "categoryId", "in": "query", "schema": {"type": "string"}, "example": "660a840da82261fe2fb610c9"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Category"], "summary": "Get category List (With Product Count)", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1001, "priceListId": "64156686ea18a9f9411d6664", "hideOutOfStock": true, "branchId": "63c124e6efa1790013071f93"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/configuration/tax": {"post": {"tags": ["Configuration > Tax"], "summary": "Add and Edit Tax", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "type": "SINGLE", "taxName": ["VAT"], "taxCalculation": ["FLAT_VALUE"], "taxRate": ["154"], "status": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Configuration > Tax"], "summary": "Get Tax and Taxlist", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "taxId", "in": "query", "schema": {"type": "string"}, "example": "63ca73364132dfa0be311fe6"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "description": "ALL/ACTIVE/INACTIVE ", "example": "ALL"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Configuration > Tax"], "summary": "Detele Tax", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "taxId", "in": "query", "schema": {"type": "string"}, "example": "637e20c1cb50a66e083241ef"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Configuration > Tax"], "summary": "Change Tax Status", "requestBody": {"content": {}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "taxId", "in": "query", "schema": {"type": "string"}, "example": "637df93b47430c10c44469a0"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "description": "ACTIVE / INACTIVE", "example": "INACTIVE"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/configuration/master-tax": {"post": {"tags": ["Configuration > Master tax"], "summary": "Update Master Tax Setting", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1035, "enableTax": true, "price": "INCLUDE", "defaultTax": "637df93b47430c10c44469a0", "universalTax": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Configuration > Master tax"], "summary": "Get Master Tax Setting", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1035"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSheet/priceExport": {"post": {"tags": ["Datasheet"], "summary": "Export price sheet", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "dataType": "PRICE", "operationType": "UPDATE", "apiVersion": 2}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSheet/inventoryExport": {"post": {"tags": ["Datasheet"], "summary": "Export inventory sheet", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "dataType": "INVENTORY", "operationType": "UPDATE", "apiVersion": 2}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/masterData": {"get": {"tags": ["Data Sync"], "summary": "Get Master Data", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/categories": {"get": {"tags": ["Data Sync"], "summary": "Get Category List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "isInitialSync", "in": "query", "schema": {"type": "boolean"}, "description": "optional field. If exist then must be either \"true\" or \"false\"", "example": "true"}, {"name": "lastSyncedAt", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the ISOString Date", "example": "2025-06-02T16:28:49.757Z"}, {"name": "cursor", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the _id of the User Role", "example": "6388487c7ef5eb0012323057"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/products": {"get": {"tags": ["Data Sync"], "summary": "Get Product List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1118"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "200"}, {"name": "isInitialSync", "in": "query", "schema": {"type": "boolean"}, "description": "optional field. If exist then must be either \"true\" or \"false\"", "example": "true"}, {"name": "lastSyncedAt", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the ISOString Date", "example": "2025-07-15T14:41:58.907Z"}, {"name": "cursor", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the _id of the User Role", "example": "6405854037a87ab2e5bcb0d7"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Data Sync"], "summary": "Get Deleted Product List", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "perPage": 2}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/images": {"get": {"tags": ["Data Sync"], "summary": "Get Image List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Data Sync"], "summary": "Get Deleted Image List", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1119, "perPage": 2, "lastSyncedAt": "2025-07-27T19:03:10.887Z", "cursor": "6666e8812c7c677356aac47e"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/favoriteProducts": {"get": {"tags": ["Data Sync"], "summary": "Get Favorite Product List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/deals": {"get": {"tags": ["Data Sync"], "summary": "Get Deal List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/dealProducts": {"post": {"tags": ["Data Sync"], "summary": "Get Deal Product List", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "dealIds": ["64638c1bbce4f83c05c81276", "647f0f4d9a1bed63a46da77f", "646387c7bce4f83c05c80f8d", "647d887b3428a671708ffb59"], "perPage": 10, "lastSyncedAt": "2023-06-05T07:12:29.152Z", "cursor": "647d88993428a671708ffb8b"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/cartItems": {"get": {"tags": ["Data Sync"], "summary": "Get Cart Item List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "2"}, {"name": "lastSyncedAt", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the ISOString Date", "example": "2025-05-29T07:52:20.513Z"}, {"name": "cursor", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the _id of the User Role", "example": "683812342a316f0179194020"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Data Sync"], "summary": "<PERSON>t Item Sync", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": [{"_id": "642123d7000daf92980c5b38", "last_cart_action": "ADD_ITEM_TO_CART", "cart_id": "1114_6413ee63eaf2900012533507_636b90ca5fb42c001210d16e", "tenant_id": 1114, "product_variant_id": "63aea629354db1001215466b", "item_number": "101", "product_name": "Heater", "product_secondary_name": "pp", "master_price_id": "637c687c4f63130012f43d15", "base_price": 440, "tax": 64, "quantity": 4, "min_qty": 4, "uom_id": "637c9d7884b9540012d6aadc", "uom_name": "A", "item_comment": [], "customer_user_role_id": "6413ee63eaf2900012533507", "sales_user_role_id": "63bd0fa3282c53001268f434", "tax_calculation_info": {"tax_id": "639987c6281ecb0012eb2232", "type": "GROUP", "tax_name": "CGST + SGST ", "calculated_tax": 64, "group_taxes": {"63bbb1372aea3a5f887b4d0e": {"tax_id": "63bbb1372aea3a5f887b4d0e", "tax_name": "CGST", "tax_rate": 10, "calculated_tax": 44, "tax_calculation": "PERCENTAGE"}, "63bbb1372aea3a5f887b4d10": {"tax_id": "63bbb1372aea3a5f887b4d10", "tax_name": "SGST", "tax_rate": 20, "calculated_tax": 20, "tax_calculation": "FLAT_VALUE"}}, "price": "EXCLUDE"}, "original_price": 440, "created_by": "62e275ac0b77a6bbdf16fac8", "update_by": "62e275ac0b77a6bbdf16fac8", "created_at": "2023-03-27T05:04:23.193Z", "updated_at": "2023-03-27T05:04:23.193Z", "action": "create"}, {"_id": "641d986b000daf9298f39712", "last_cart_action": "ADD_ITEM_TO_CART", "cart_id": "1114_639c2d6d5359ee0012333664_634d018c98a6bf0012d2303a", "tenant_id": 1114, "product_variant_id": "63aea629354db1001215466b", "item_number": "101", "product_name": "Heater", "product_secondary_name": "pp", "master_price_id": "637c687c4f63130012f43d15", "base_price": 440, "tax": 64, "quantity": 4, "min_qty": 4, "uom_id": "637c9d7884b9540012d6aadc", "uom_name": "A", "item_comment": [], "customer_user_role_id": "639c2d6d5359ee0012333664", "sales_user_role_id": "63bd0fa3282c53001268f434", "tax_calculation_info": {"tax_id": "639987c6281ecb0012eb2232", "type": "GROUP", "tax_name": "CGST + SGST ", "calculated_tax": 64, "group_taxes": {"63bbb1372aea3a5f887b4d0e": {"tax_id": "63bbb1372aea3a5f887b4d0e", "tax_name": "CGST", "tax_rate": 10, "calculated_tax": 44, "tax_calculation": "PERCENTAGE"}, "63bbb1372aea3a5f887b4d10": {"tax_id": "63bbb1372aea3a5f887b4d10", "tax_name": "SGST", "tax_rate": 20, "calculated_tax": 20, "tax_calculation": "FLAT_VALUE"}}, "price": "EXCLUDE"}, "original_price": 440, "created_by": "633bcd714018180011efc6be", "update_by": "633bcd714018180011efc6be", "created_at": "2023-03-24T12:32:42.992Z", "updated_at": "2023-03-24T12:32:42.992Z", "action": "update"}]}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/orders": {"get": {"tags": ["Data Sync"], "summary": "Get Order List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "2"}, {"name": "lastSyncedAt", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the ISOString Date", "example": "2025-06-26T09:55:27.925Z"}, {"name": "cursor", "in": "query", "schema": {"type": "string"}, "description": "optional field. If exist then must be the _id of the User Role", "example": "685d13e6f60a980d2f1bd09d"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/dataSync/cartItems/customerSync": {"post": {"tags": ["Data Sync"], "summary": "Customer-Specific Cart Sync", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "customerUserRoleId": "639c2d6d5359ee0012333664", "cartId": "1114_639c2d6d5359ee0012333664_634d018c98a6bf0012d2303a", "cartItems": []}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/deal": {"post": {"tags": ["Deals > Deal"], "summary": "Create Deal", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "priceId": "637c687c4f63130012f43d15", "dealType": "DISCOUNT"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Deals > Deal"], "summary": "Get Deal list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1118"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "patch": {"tags": ["Deals > Deal"], "summary": "Update Deal status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1119, "status": "ARCHIVED", "dealIds": ["64942faa6e995d0aedebce6a", "65d464315a8c82581bf059d6"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Deals > Deal"], "summary": "Update deal details", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"dealId": "666c2e449df92ca88716a90d", "tenantId": 1118, "dealName": "Test Final Price", "secondaryDealName": "Test Final Price Arabic", "dealFromDate": "2024-06-14T11:49:24.242Z", "dealToDate": "2024-06-15T18:29:59.595Z", "salesPersonId": ["63d7cce3099872001245d906", "6344059ea2a6e700126d2a54", "6555ced41eaff80012da551b", "6621394d50e6570012327547", "665d65d1ed2eba00127505cf"], "dealSortType": "MANUAL", "products": [{"dealProductId": "666c2e679df92ca88716a922", "dealType": "DISCOUNT", "discountType": "PERCENT", "percent": 10.2, "amount": 1.02, "discountedPrice": 560}, {"dealProductId": "666c2e679df92ca88716a927", "dealType": "DISCOUNT", "discountType": "PERCENT", "percent": 23.12, "amount": 4.62, "discountedPrice": 50}]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Deals > Deal"], "summary": "Delete Deals", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/deal/updateProductList": {"post": {"tags": ["Deals > Update Product List"], "summary": "Update Product Status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "dealId": "64564a0f02f55944c1fa4742", "dealProductId": ["645a0ccff0f31b9ff6f45c10", "645a0ccff0f31b9ff6f45c13", "645a0ccff0f31b9ff6f45c16"], "status": "ACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Deals > Update Product List"], "summary": "Update Product Sequence", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "dealProductId": "645a0ccff0f31b9ff6f45c16", "dealId": "6459d47d6b3d9da748e65b9e", "dealProductSequence": 350}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Deals > Update Product List"], "summary": "Get Next Products List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "dealId", "in": "query", "schema": {"type": "string"}, "example": "64638c1bbce4f83c05c81276"}, {"name": "dealProductSequence", "in": "query", "schema": {"type": "integer"}, "example": "300"}, {"name": "nextDealProductCount", "in": "query", "schema": {"type": "integer"}, "example": "3"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Deals > Update Product List"], "summary": "Delete Deal Product", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/deal/dealsProductList": {"post": {"tags": ["Deals > Deals Product List"], "summary": "Get Product List by Item Numbers", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "priceId": "6374bd481f6d6bdec112ee96", "page": 1, "perPage": 20, "itemNumbers": ["6606", "132", "dfd", "Ryan1012"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Deals > Deals Product List"], "summary": "Get Running Deals List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "priceId", "in": "query", "schema": {"type": "string"}, "example": "6374bd481f6d6bdec112ee96"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/deal/dealProduct": {"post": {"tags": ["Deals > Deal Product"], "summary": "Add product in deal", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "dealId": "64638c1bbce4f83c05c81276", "products": [{"productId": "64661c6861324c754acfe9dc"}], "dealFromDate": "2023-06-06T00:00:00.000Z", "dealToDate": "2023-06-10T00:00:00.000Z"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Deals > Deal Product"], "summary": "Get Current Deal Product List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "100"}, {"name": "priceId", "in": "query", "schema": {"type": "string"}, "example": "6374bd481f6d6bdec112ee96"}, {"name": "dealId", "in": "query", "schema": {"type": "string"}, "example": "66b481ac8f26bf821e6860d1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/deal/checkValidItems": {"post": {"tags": ["Deals"], "summary": "Check Valid Items", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1118, "dealId": "6490098d422008c3c00c13f6", "priceId": "63983f77aa1d4e0011dfa103", "dealType": "DISCOUNT", "itemNumbers": ["efawed8", "SR"], "dealFromDate": "2023-06-19T07:53:48.484Z", "dealToDate": "2023-06-27T18:29:59.595Z"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/deal/detail": {"get": {"tags": ["Deals"], "summary": "Get Deal Details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "dealId", "in": "query", "schema": {"type": "string"}, "example": "64564a0f02f55944c1fa4742"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/deal/productList": {"get": {"tags": ["Deals"], "summary": "Product list for deal", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "100"}, {"name": "dealFromDate", "in": "query", "schema": {"type": "string"}, "description": "Prod: 2024-09-11T05:36:24.251Z", "example": "2024-09-12T08:28:00.000Z"}, {"name": "dealToDate", "in": "query", "schema": {"type": "string"}, "description": "Prod: 2024-09-12T20:59:59.595Z", "example": "2024-10-18T18:29:59.595Z"}, {"name": "priceId", "in": "query", "schema": {"type": "string"}, "description": "Prod: 647e49113c92ecdd432390b7", "example": "6374bd481f6d6bdec112ee96"}, {"name": "dealId", "in": "query", "schema": {"type": "string"}, "description": "Prod: 66b0c8e40f2ce7ed9e5ff705", "example": "66b481ac8f26bf821e6860d1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/deal/dealStatistics": {"post": {"tags": ["Deals"], "summary": "Update Deal Statistics (click/view)", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"dealId": "647f0f4d9a1bed63a46da77f", "dealProductId": "64807a99b0fa86f117a3664d", "tenantId": 1131}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/imageV2/getUploadSignature": {"post": {"tags": ["Images"], "summary": "Get signature for image", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "imageName": "dfd_P1.png"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "OK", "headers": {"X-Powered-By": {"schema": {"type": "string", "example": "Express"}}, "Access-Control-Allow-Origin": {"schema": {"type": "string", "example": "*"}}, "Access-Control-Expose-Headers": {"schema": {"type": "string", "example": "refreshed-access-token"}}, "Cache-Control": {"schema": {"type": "string", "example": "no-cache"}}, "Content-Type": {"schema": {"type": "string", "example": "application/json; charset=utf-8"}}, "Content-Length": {"schema": {"type": "integer", "example": "655"}}, "ETag": {"schema": {"type": "string", "example": "W/\"28f-VnJwWRwd4gGh8zh957PNqyNfbQE\""}}, "Date": {"schema": {"type": "string", "example": "Mon, 17 Apr 2023 13:39:07 GMT"}}, "Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Keep-Alive": {"schema": {"type": "string", "example": "timeout=5"}}}, "content": {"application/json": {"schema": {"type": "object"}, "example": {"message": "upload_url_get_success", "data": {"signedUrl": "https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/64394235bdb2793f615d83c2_P1.JPEG?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIATGBACB6QFTN5S5EP%2F20230417%2Fme-south-1%2Fs3%2Faws4_request&X-Amz-Date=20230417T133905Z&X-Amz-Expires=300&X-Amz-Signature=f6cee5d21e91e73641429b7f5882b6269dc4692960b384b0bb229fe04570f2d9&X-Amz-SignedHeaders=host", "s3Url": "https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/64394235bdb2793f615d83c2_P1.JPEG", "imageName": "64394235bdb2793f615d83c2_P1.JPEG", "productVariantId": "64394235bdb2793f615d83c2"}}}}}}}}, "/productService/imageV2": {"post": {"tags": ["Images"], "summary": "Add Image", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1119, "s3Url": "https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/product/web/1114/643577975989e41e5eeb060e_P1.jpeg", "imageSize": 11140, "imageName": "645b22e0eaa2d77e9058e783_645b22e0eaa2d77e9058e788_P1.jpeg", "groupId": "645b22e0eaa2d77e9058e788", "productVariantId": "645b22e0eaa2d77e9058e783"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Images"], "summary": "delete images", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1004"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Images"], "summary": "Image listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1118"}, {"name": "imageType", "in": "query", "schema": {"type": "string"}, "description": "ALL / NEW / NEED_IMAGES", "example": "NEED_IMAGES"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "7kg"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/imageV2/swapProductImages": {"post": {"tags": ["Images"], "summary": "Swap images", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"imageIds": ["63ad7afc251da20a3613554d", "63ad7afc251da20a36135550"], "tenantId": 1114}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/imageV2/imagesMatch": {"get": {"tags": ["Images"], "summary": "Get image match product list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "linkedTenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "testing"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Images"], "summary": "Image match action", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "linkedTenantId": 1119, "type": "SELECTED", "product": [{"productId": "63f5b1be478135a96edd68dd", "linkedTenantProductId": "63ad1a8d43ff4500122d20c6"}, {"variantId": "63f34b2e53ff820a78b4d090", "parentId": "63f34b2e53ff820a78b4d098", "linkedTenantVariantId": "63b02100ecf078002c555491", "linkedParentId": "642bb8f8a5ac388d302e9e1c"}]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/imageV2/imagesByItemNumbers": {"post": {"tags": ["Images"], "summary": "Get Images By Item Numbers", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": "{\n    \"tenantId\": {{tenantId}},\n    \"itemNumbers\": [\n        \"TRT71662\",\n        \"ABC-abc-5678\",\n        \"78CUFI92\",\n        \"Cr1002\",\n        \"dfd\",\n        \"78CUFI91\",\n        \"TRT71663\",\n        \"ABC-abc-1234\",\n        \"78CUFI93\",\n        // Production\n        \"W14-0683\",\n        \"W14-0956\",\n        \"W14-0957\",\n        \"W14-0958\",\n        \"K11639\",\n        \"K11640\",\n        \"K11641\",\n        \"K11642\",\n        \"K11643\",\n        \"K11644\"\n    ]\n}"}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/attribute": {"post": {"tags": ["Master Data > Attribute"], "summary": "Add Attribute", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "attributeName": "<PERSON><PERSON><PERSON><PERSON>", "secondaryLanguageAttributeName": "Boghttle"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Attribute"], "summary": "Edit Attribute", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "63c8eba84b8de14dd4400595", "isActive": true, "tenantId": 1131, "attributeName": "Helmegt kings", "secondaryLanguageAttributeName": "Earbuddds"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Attribute"], "summary": "Get Attribute List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "d"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "20"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Attribute"], "summary": "Delete Attribute", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/attributeSet": {"post": {"tags": ["Master Data > Attribute Set"], "summary": "Add Attribute Set", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "attributeSetName": "Office Products", "secondaryLanguageAttributeSetName": "office chiz"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Attribute Set"], "summary": "Edit Attribute Set", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "63724673f46f0cb0dc66fef5", "isActive": true, "tenantId": 1131, "attributeSetName": "Power Tools", "secondaryLanguageAttributeSetName": "urja samgri tools"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Attribute Set"], "summary": "Get Attribute Set List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "gri"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Attribute Set"], "summary": "Delete Attribute Set", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/brand": {"post": {"tags": ["Master Data > Brand"], "summary": "Add Brand", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "brandName": "Dell", "secondaryLanguageBrandName": "del"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Brand"], "summary": "Edit Brand", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "637331be585729637044fb5c", "isActive": true, "tenantId": 1131, "brandName": "philips", "secondaryLanguageBrandName": "philips t"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Brand"], "summary": "Get Brand List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "2"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "2"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Brand"], "summary": "Delete Brand", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/unit": {"post": {"tags": ["Master Data > Unit"], "summary": "Add Unit", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": true, "tenantId": 1131, "unitName": "indiUnit", "secondaryLanguageUnitName": "indi"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Unit"], "summary": "Edit Unit", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "6373522431cd87bd062f72e2", "isActive": true, "tenantId": 1131, "unitName": "KG", "secondaryLanguageUnitName": "ke giii"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Unit"], "summary": "Get Unit List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Unit"], "summary": "Delete Unit", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/price": {"post": {"tags": ["Master Data > Price"], "summary": "Add Price", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"isActive": false, "tenantId": 1131, "priceName": "Battery Capacity", "secondaryLanguagePriceName": "Second Capacity"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Master Data > Price"], "summary": "Edit Price", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"_id": "63c7ee8bf692a06e910ef5f8", "isActive": "true", "tenantId": 1131, "priceName": "capacity", "secondaryLanguagePriceName": "capacity"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Price"], "summary": "Get Price List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ALL"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "200"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Master Data > Price"], "summary": "Delete Price", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/attributeAssociation": {"put": {"tags": ["Master Data > Attribute Association"], "summary": "Update Attribute Association", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "attributeSetId": "63724673f46f0cb0dc66fef5", "attributeIds": ["6374a9ed4dfc96001260858c", "6371d602f4d7553be184b791", "6371de646c6c8e71cec916b3", "sdsd"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Master Data > Attribute Association"], "summary": "Get Attribute Association List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "status", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "_id", "in": "query", "schema": {"type": "string"}, "example": "63731436e604224cd8124845"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/masterData/updateStatus": {"put": {"tags": ["Master Data"], "summary": "Update Status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"entity": "PRICE", "tenantId": 1131, "isActive": false, "ids": ["6373538e31cd87bd062f72ec", "6374bf41cc15a0e922008be2"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/cart": {"post": {"tags": ["Order > Cart"], "summary": "cartActions", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1272, "masterPriceId": "68381a318bcde1af6a265d9d", "uomId": "6470be241ae26f9afb04b35d", "uomName": "Electronics", "quantity": 10, "minQty": 10, "itemComment": "Item Kelvinator Comments", "productVariantId": "683849658bcde1af6a266417", "salesPersonRoleId": "6838184f9a31b1d795e5c8f4", "customerUserRoleId": "68383bb39a31b1d795e5ed15", "actionType": "ADD_ITEM_TO_CART", "tax": 0}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Order > Cart"], "summary": "cartDetails", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "customerUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "6501ad7b40d5660012f06593"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Order > Cart"], "summary": "delet cart items", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "customerUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "63fc90d06085cd0012c03617"}, {"name": "cartItemIds", "in": "query", "schema": {"type": "string"}, "example": "64882324d32a5bf87f82fe98"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/itemCount": {"get": {"tags": ["Order > Cart"], "summary": "cartItemCount", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "customerUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "63b25f79989ff60011a1ff50"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/order/orderStats": {"get": {"tags": ["Order > Stats"], "summary": "user role stats", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "userRoles", "in": "query", "schema": {"type": "string"}, "example": "6371b561f6eba60012a75bc0"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/draft": {"post": {"tags": ["Order > Drafts"], "summary": "create draft", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1119, "customerUserRoleId": "63fc90d06085cd0012c03617", "salesPersonRoleId": "6363b6d59a0d2f00129cb973", "customerName": "MS Dhoni POSTMAN", "customerPrimaryContactName": "<PERSON><PERSON>i <PERSON>", "customerId": "111910026", "externalId": "23"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Order > Drafts"], "summary": "draft details api", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "draftId", "in": "query", "schema": {"type": "string"}, "example": "64882f09a3400ecc1274f33f"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/drafts": {"get": {"tags": ["Order > Drafts"], "summary": "draft listing API", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "sd"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "delete": {"tags": ["Order > Drafts"], "summary": "delete drafts api", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "draftIds", "in": "query", "schema": {"type": "string"}, "example": "646333a8b1802ecac1d522f0"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/placeOrderFromDraft": {"post": {"tags": ["Order > Drafts"], "summary": "Order from Draft", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"draftId": "646252305cd66039ff24e7fd", "tenantId": 1119, "salesPersonRoleId": "6363b6d59a0d2f00129cb973", "customerUserRoleId": "63b25f79989ff60011a1ff50", "customerName": "Ketan POSTMAN", "customerLegalName": "POSTMAN LEGAL NAME", "salePersonName": "Ketan Sales Person New POSTMAN", "branchId": "633aaf5585b9b200129a4503", "externalId": "ewqeqdsa", "orderAppType": "SALES_APP", "orderPunchDeviceType": "MOBILE", "orderPunchDeviceOs": "IOS", "orderRemark": "Testing remarks", "ShippingAddress": "Testing Address of the customer or selected address", "cityId": "631ffc5c89881e0012660b3b", "regionId": "630c58a6bfb2982b1cbcd4cf", "shippingMobileNumber": 9685635896, "shippingCountryCode": "+91", "regionName": "Gujarat", "cityName": "Surat", "shippingCoordinates": {"lat": 20.121, "lng": 72}, "customerPrimaryContactName": "Customer firstname and last name POSTMAN", "masterPriceId": "63906a580eb78c00125ddba8"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/draftToCart": {"post": {"tags": ["Order > Drafts"], "summary": "Draft to cart", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"draftId": "648828a11a76cdf00283115f", "tenantId": 1119, "customerUserRoleId": "63fc90d06085cd0012c03617"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/exportTask": {"post": {"tags": ["Order > Export"], "summary": "Export Orders", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1241, "startDate": "2024-05-01", "endDate": "2024-07-27", "timezone": "Asia/Kolkata", "orderStatusList": ["PENDING", "RECEIVED", "RELEASED", "PREPARING", "SHIPPED", "DELIVERED", "CANCELLED"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/order/orders": {"get": {"tags": ["Order > Orders"], "summary": "Order listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1004"}, {"name": "apiVersion", "in": "query", "schema": {"type": "integer"}, "example": "2"}, {"name": "salesPersonRoleId", "in": "query", "schema": {"type": "string"}, "example": "6414283cc80b530012f43163"}, {"name": "orderAppType", "in": "query", "schema": {"type": "string"}, "example": "SALES_APP"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Order"], "summary": "Update Order Status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "orderIds": ["685e8e49a5c14e7f2a06e100"], "orderStatus": "DELIVERED"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/order": {"get": {"tags": ["Order"], "summary": "Get order detail", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "orderId", "in": "query", "schema": {"type": "string"}, "example": "6667f848daa19f09e04c6730"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "apiVersion", "in": "query", "schema": {"type": "integer"}, "example": "2"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Order"], "summary": "Update Customer With Orders", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"customerUserRoleId": "641b093ef4fac2a95c0e77d3", "updateInformation": {"shippingMobileNumber": 9685635896}, "apiVersion": 2}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "post": {"tags": ["Order"], "summary": "Place order", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1004, "customerUserRoleId": "640c6450cc730200125a4e21", "salesPersonRoleId": "653679b915fe6900120984b9", "customerName": "<PERSON><PERSON>", "customer_legal_name": "Foram Pvt Ltd", "salePersonName": "<PERSON>", "branchId": "636a0e0587526f00125f886f", "orderPunchDeviceType": "MOBILE", "orderPunchDeviceOs": "ANDROID", "orderAppType": "SALES_APP", "customerPrimaryContactName": "<PERSON><PERSON>", "itemComment": "Item Kelvinator Comments", "orderRemark": "<PERSON><PERSON><PERSON> remarks", "ShippingAddress": "32A, Nehru Society, Dahegam, Gujarat 382305, India", "cityId": "64709595e90a2700125dc2ce", "regionId": "", "shippingMobileNumber": 0, "shippingCountryCode": "", "regionName": "", "cityName": "<PERSON><PERSON><PERSON>", "shippingCoordinates": {"lat": 23.0106567315333, "lng": 72.************}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "devicetype", "in": "header", "schema": {"type": "string"}, "example": "{{devicetype}}"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "example": "MOBILE"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/sendReasonMessage": {"post": {"tags": ["Order"], "summary": "Send Hold Reason Message", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "orderId": "687f6098fdd541af319683ac", "holdReasonId": "658bf22c096cd031d1124abd", "messageDetails": {"salesPerson": {"templateId": "7b417d51-63c5-4077-9ca1-4fff1a52b752", "language": "en", "inputs": {"order_value": "1007"}}, "customer": {"templateId": "94ad1db4-44b0-48cf-a495-22b673233683", "language": "en", "inputs": {}}}, "isReleaseMessage": false, "isSendMessage": true}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/order/dashboardSummary": {"get": {"tags": ["Order"], "summary": "Dashboard summary", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "branchId", "in": "query", "schema": {"type": "string"}, "example": "633aaf5585b9b200129a4503"}, {"name": "apiVersion", "in": "query", "schema": {"type": "integer"}, "example": "2"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/order/orderReport": {"get": {"tags": ["Order"], "summary": "Order Reports", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "2"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "2"}, {"name": "startDate", "in": "query", "schema": {"type": "string"}, "example": "2023-01-01"}, {"name": "endDate", "in": "query", "schema": {"type": "string"}, "example": "2023-10-10"}, {"name": "timezone", "in": "query", "schema": {"type": "string"}, "example": "Asia/Kolkata"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/checkOrderUsers": {"get": {"tags": ["Order"], "summary": "Check order email", "parameters": [{"name": "token", "in": "query", "schema": {"type": "string"}, "example": "63c160e4da461e0012432ee3"}, {"name": "orderId", "in": "query", "schema": {"type": "string"}, "example": "6486e94a5fc430e9cf1e109f"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/status": {"get": {"tags": ["Order"], "summary": "Order Status List", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/orderV2/checkPreApproved": {"put": {"tags": ["Order"], "summary": "Order pre approved", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1241, "externalId": "C20311", "orderId": "6639e7d2430be360c93d665f"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2": {"post": {"tags": ["Product > Product"], "summary": "Add Product", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1170, "product": {"isActive": true, "attributeSet": "6522623ff2c7a7d10ec5376f", "attributes": [{"value": "12inch", "type": "MANUAL", "attribute_id": "6522628ff2c7a7d10ec53e04"}, {"value": "500", "type": "MANUAL", "attribute_id": "652262bef2c7a7d10ec53f6a"}, {"value": "6", "type": "MANUAL", "attribute_id": "652262adf2c7a7d10ec53eac"}], "title": "Water Pump Plier", "secondaryLanguageTitle": "كماشة مضخة مياه", "itemNumber": "M0861", "barcodes": [], "description": "Water Pump Pliers are versatile hand tools commonly used in plumbing, automotive, and electrical work. They feature adjustable jaws that can grip a variety of sizes and shapes, making them ideal for gripping and turning pipes, fittings, nuts, bolts, and other irregularly shaped objects.", "secondaryLanguageDescription": "كماشة مضخة المياه هي أدوات يدوية متعددة الاستخدامات شائعة الاستخدام في أعمال السباكة والسيارات والكهرباء. تتميز بفك قابل للتعديل يمكنه الإمساك بمجموعة متنوعة من الأحجام والأشكال، مما يجعلها مثالية للإمساك بالأنابيب والتجهيزات والصواميل والمسامير وغيرها من الأشياء غير المنتظمة.", "type": "PARENT", "brand": "6415665dea18a9f9411d6652", "family": "6419a2a75cfb741a6f7080da", "category": "6419d12b5cfb741a6f70a630", "subCategory": "6419d18a5cfb741a6f70a72b", "tags": ["Pump", "Pliers"], "variants": {"type": "Size", "values": ["8inch", "12inch"]}, "taxId": "6415656bea18a9f9411d653d", "groups": {"type": "Color", "values": ["Red", "Black"]}}, "variantProducts": [{"isActive": true, "itemNumber": "\tK12102", "uomMapping": {"uom": "6415659cea18a9f9411d65ee", "qtyCtn": "12", "minQty": "12"}, "priceMapping": [{"price": 51.15, "master_price_id": "671b7806f2a0800aeb6418ef"}, {"price": 51.515, "master_price_id": "64156686ea18a9f9411d6664"}, {"price": 155.515, "master_price_id": "647e49113c92ecdd432390b7"}, {"price": 79.581, "master_price_id": "64bbacf7d28ff53223dc6fc3"}, {"price": 2843.181, "master_price_id": "64bbad34d28ff53223dc6ffd"}, {"price": 283.1581, "master_price_id": "64bbad57d28ff53223dc7048"}], "inventoryMapping": [{"warehouse_id": "63c124e6efa1790013071f95", "branch_id": "671b72dadf056899fc2f479b", "quantity": 1000}], "barcodes": [], "groupName": "Red", "variantName": "8inch"}, {"isActive": true, "itemNumber": "\tK12103", "uomMapping": {"uom": "6415659cea18a9f9411d65ee", "qtyCtn": "6", "minQty": "6"}, "priceMapping": [{"price": 246, "master_price_id": "671b7806f2a0800aeb6418ef"}, {"price": 51.518, "master_price_id": "64156686ea18a9f9411d6664"}, {"price": 25.051, "master_price_id": "647e49113c92ecdd432390b7"}, {"price": 125.51, "master_price_id": "64bbacf7d28ff53223dc6fc3"}, {"price": 2181.515, "master_price_id": "64bbad34d28ff53223dc6ffd"}, {"price": 2518.518, "master_price_id": "64bbad57d28ff53223dc7048"}], "inventoryMapping": [{"warehouse_id": "63c124e6efa1790013071f95", "branch_id": "671b72dadf056899fc2f479b", "quantity": 1800}], "barcodes": [], "groupName": "Red", "variantName": "12inch"}, {"isActive": true, "itemNumber": "\tK12104", "uomMapping": {"uom": "6415659cea18a9f9411d65ee", "qtyCtn": "12", "minQty": "12"}, "priceMapping": [{"price": 151, "master_price_id": "671b7806f2a0800aeb6418ef"}, {"price": 984.1, "master_price_id": "64156686ea18a9f9411d6664"}, {"price": 1813.051, "master_price_id": "647e49113c92ecdd432390b7"}, {"price": 125.158, "master_price_id": "64bbacf7d28ff53223dc6fc3"}, {"price": 789.51, "master_price_id": "64bbad34d28ff53223dc6ffd"}, {"price": 78, "master_price_id": "64bbad57d28ff53223dc7048"}], "inventoryMapping": [{"warehouse_id": "63c124e6efa1790013071f95", "branch_id": "671b72dadf056899fc2f479b", "quantity": 500}], "barcodes": [], "groupName": "Black", "variantName": "8inch"}, {"isActive": true, "itemNumber": "\tK12105", "uomMapping": {"uom": "6415659cea18a9f9411d65ee", "qtyCtn": "6", "minQty": "6"}, "priceMapping": [{"price": 25.51, "master_price_id": "671b7806f2a0800aeb6418ef"}, {"price": 81.51, "master_price_id": "64156686ea18a9f9411d6664"}, {"price": 51351.5, "master_price_id": "647e49113c92ecdd432390b7"}, {"price": 862.515, "master_price_id": "64bbacf7d28ff53223dc6fc3"}, {"price": 219.515, "master_price_id": "64bbad34d28ff53223dc6ffd"}, {"price": 152, "master_price_id": "64bbad57d28ff53223dc7048"}], "inventoryMapping": [{"warehouse_id": "63c124e6efa1790013071f95", "branch_id": "671b72dadf056899fc2f479b", "quantity": 784}], "barcodes": [], "groupName": "Black", "variantName": "12inch"}]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Product > Product"], "summary": "Product details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1241"}, {"name": "productId", "in": "query", "schema": {"type": "string"}, "example": "6572b154ac7f5859a950e729"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Product > Product"], "summary": "Edit Product", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1114, "product": {"productId": "643e3d57fb03aec65b5eb50e", "barcodes": [], "isActive": true, "title": "18th April Product", "secondaryLanguageTitle": "18th April Product", "itemNumber": "18AP", "type": "PARENT", "attributes": [], "brand": "637d9c62e1bcfc001292df0d", "family": "6374d36b421cf4001212f040", "tags": []}, "variantProducts": [{"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb513_643e3d57fb03aec65b5eb516", "variantName": "Red", "isActive": true, "itemNumber": "fggf", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "640b15770d73f2831601afd3", "price": 32}, {"master_price_id": "64242f0edfcbe65012804bb7", "price": 12}, {"master_price_id": "637c687c4f63130012f43d15", "price": 2}, {"master_price_id": "63cf74ccce91dc4b3504eb97", "price": 43}, {"master_price_id": "63abfca44fac7e0012211a06", "price": 23}, {"master_price_id": "63abfcac4fac7e0012211a0c", "price": 11}, {"master_price_id": "63c904016659a0c718f06cab", "price": 21}, {"master_price_id": "63c904126659a0c718f06cb1", "price": 43}, {"master_price_id": "63abfc9c4fac7e00122119fc", "price": 32}, {"master_price_id": "63c903e36659a0c718f06ca2", "price": 22}, {"master_price_id": "639035510eb78c00125dd91c", "price": 22}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}], "barcodes": ["Tenant", "vvhd"], "groupName": "s"}, {"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb514_643e3d57fb03aec65b5eb516", "variantName": "Violet", "isActive": true, "itemNumber": "hhfhfh", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "640b15770d73f2831601afd3", "price": 12}, {"master_price_id": "64242f0edfcbe65012804bb7", "price": 23}, {"master_price_id": "637c687c4f63130012f43d15", "price": 11}, {"master_price_id": "63cf74ccce91dc4b3504eb97", "price": 23}, {"master_price_id": "63abfca44fac7e0012211a06", "price": 22}, {"master_price_id": "63abfcac4fac7e0012211a0c", "price": 11}, {"master_price_id": "63c904016659a0c718f06cab", "price": 14}, {"master_price_id": "63c904126659a0c718f06cb1", "price": 54}, {"master_price_id": "63abfc9c4fac7e00122119fc", "price": 33}, {"master_price_id": "63c903e36659a0c718f06ca2", "price": 22}, {"master_price_id": "639035510eb78c00125dd91c", "price": 1}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}], "barcodes": ["violet"], "groupName": "s"}, {"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb515_643e3d57fb03aec65b5eb516", "variantName": "Purple", "isActive": true, "itemNumber": "hdgdg", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "640b15770d73f2831601afd3", "price": 32}, {"master_price_id": "64242f0edfcbe65012804bb7", "price": 12}, {"master_price_id": "637c687c4f63130012f43d15", "price": 2}, {"master_price_id": "63cf74ccce91dc4b3504eb97", "price": 43}, {"master_price_id": "63abfca44fac7e0012211a06", "price": 23}, {"master_price_id": "63abfcac4fac7e0012211a0c", "price": 11}, {"master_price_id": "63c904016659a0c718f06cab", "price": 21}, {"master_price_id": "63c904126659a0c718f06cb1", "price": 43}, {"master_price_id": "63abfc9c4fac7e00122119fc", "price": 32}, {"master_price_id": "63c903e36659a0c718f06ca2", "price": 22}, {"master_price_id": "639035510eb78c00125dd91c", "price": 22}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}], "barcodes": ["hfhhf", "vvhd"], "groupName": "s"}, {"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb513_643e3d57fb03aec65b5eb517", "variantName": "Red", "isActive": true, "itemNumber": "gfgdg", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "640b15770d73f2831601afd3", "price": 12}, {"master_price_id": "64242f0edfcbe65012804bb7", "price": 23}, {"master_price_id": "637c687c4f63130012f43d15", "price": 11}, {"master_price_id": "63cf74ccce91dc4b3504eb97", "price": 23}, {"master_price_id": "63abfca44fac7e0012211a06", "price": 22}, {"master_price_id": "63abfcac4fac7e0012211a0c", "price": 11}, {"master_price_id": "63c904016659a0c718f06cab", "price": 14}, {"master_price_id": "63c904126659a0c718f06cb1", "price": 54}, {"master_price_id": "63abfc9c4fac7e00122119fc", "price": 33}, {"master_price_id": "63c903e36659a0c718f06ca2", "price": 22}, {"master_price_id": "639035510eb78c00125dd91c", "price": 1}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}], "barcodes": ["RedBarcode", "gtu", "vvhd"], "groupName": "M"}, {"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb514_643e3d57fb03aec65b5eb517", "variantName": "Violet", "isActive": true, "itemNumber": "gygjyg", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "640b15770d73f2831601afd3", "price": 32}, {"master_price_id": "64242f0edfcbe65012804bb7", "price": 12}, {"master_price_id": "637c687c4f63130012f43d15", "price": 2}, {"master_price_id": "63cf74ccce91dc4b3504eb97", "price": 43}, {"master_price_id": "63abfca44fac7e0012211a06", "price": 23}, {"master_price_id": "63abfcac4fac7e0012211a0c", "price": 11}, {"master_price_id": "63c904016659a0c718f06cab", "price": 21}, {"master_price_id": "63c904126659a0c718f06cb1", "price": 43}, {"master_price_id": "63abfc9c4fac7e00122119fc", "price": 32}, {"master_price_id": "63c903e36659a0c718f06ca2", "price": 22}, {"master_price_id": "639035510eb78c00125dd91c", "price": 22}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}], "barcodes": ["<PERSON><PERSON>", "ghdgf"], "groupName": "M"}, {"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb515_643e3d57fb03aec65b5eb517", "variantName": "Purple", "isActive": true, "itemNumber": "ggdgd", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "640b15770d73f2831601afd3", "price": 32}, {"master_price_id": "64242f0edfcbe65012804bb7", "price": 12}, {"master_price_id": "637c687c4f63130012f43d15", "price": 2}, {"master_price_id": "63cf74ccce91dc4b3504eb97", "price": 43}, {"master_price_id": "63abfca44fac7e0012211a06", "price": 23}, {"master_price_id": "63abfcac4fac7e0012211a0c", "price": 11}, {"master_price_id": "63c904016659a0c718f06cab", "price": 21}, {"master_price_id": "63c904126659a0c718f06cb1", "price": 43}, {"master_price_id": "63abfc9c4fac7e00122119fc", "price": 32}, {"master_price_id": "63c903e36659a0c718f06ca2", "price": 22}, {"master_price_id": "639035510eb78c00125dd91c", "price": 22}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}], "barcodes": ["gete", "frt"], "groupName": "M"}, {"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb513_643e3d57fb03aec65b5eb518", "variantName": "Red", "isActive": true, "itemNumber": "eee", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "637c687c4f63130012f43d15", "price": 2}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}], "barcodes": ["1 2 3 4 5 6 7", "1234567"], "groupName": "L"}, {"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb514_643e3d57fb03aec65b5eb518", "variantName": "Violet", "isActive": true, "itemNumber": "gfgd", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "640b15770d73f2831601afd3", "price": 12}, {"master_price_id": "64242f0edfcbe65012804bb7", "price": 23}, {"master_price_id": "637c687c4f63130012f43d15", "price": 11}, {"master_price_id": "63cf74ccce91dc4b3504eb97", "price": 23}, {"master_price_id": "63abfca44fac7e0012211a06", "price": 22}, {"master_price_id": "63abfcac4fac7e0012211a0c", "price": 11}, {"master_price_id": "63c904016659a0c718f06cab", "price": 14}, {"master_price_id": "63c904126659a0c718f06cb1", "price": 54}, {"master_price_id": "63abfc9c4fac7e00122119fc", "price": 33}, {"master_price_id": "63c903e36659a0c718f06ca2", "price": 22}, {"master_price_id": "639035510eb78c00125dd91c", "price": 1}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}], "barcodes": ["5345RMA 324", "5345RMA324"], "groupName": "L"}, {"variantId": "643e3d57fb03aec65b5eb50e_643e3d57fb03aec65b5eb515_643e3d57fb03aec65b5eb518", "variantName": "Purple", "isActive": true, "itemNumber": "ffdfs", "uomMapping": {"uom": "637d9c85e1bcfc001292df19", "minQty": 20}, "priceMapping": [{"master_price_id": "640b15770d73f2831601afd3", "price": 32}, {"master_price_id": "64242f0edfcbe65012804bb7", "price": 12}, {"master_price_id": "637c687c4f63130012f43d15", "price": 2}, {"master_price_id": "63cf74ccce91dc4b3504eb97", "price": 43}, {"master_price_id": "63abfca44fac7e0012211a06", "price": 23}, {"master_price_id": "63abfcac4fac7e0012211a0c", "price": 11}, {"master_price_id": "63c904016659a0c718f06cab", "price": 21}, {"master_price_id": "63c904126659a0c718f06cb1", "price": 43}, {"master_price_id": "63abfc9c4fac7e00122119fc", "price": 32}, {"master_price_id": "63c903e36659a0c718f06ca2", "price": 22}, {"master_price_id": "639035510eb78c00125dd91c", "price": 22}], "inventoryMapping": [{"branch_id": "6333f1fb67e853cb742142a2", "warehouse_id": "636ccd34fc3d2d01fc93c89f", "quantity": 0}, {"branch_id": "63d8f7cf8277c200121be130", "warehouse_id": "63d8f7cf8277c200121be132", "quantity": 0}, {"branch_id": "63d8f7c58277c200121be0fc", "warehouse_id": "63d8f7c58277c200121be0fe", "quantity": 0}], "barcodes": [], "groupName": "L"}]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/products": {"delete": {"tags": ["Product > Products"], "summary": "Delete Products", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1170"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "put": {"tags": ["Product > Products"], "summary": "Update product status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1118, "variantProductIds": [{"parentId": "6645b3b6abe49ad250e0bca9", "updateIds": ["6645b3b6abe49ad250e0bcb0", "6645b3b6abe49ad250e0bcb1"]}, {"parentId": "66436266589c7f8d6cbc3745", "updateIds": ["66436266589c7f8d6cbc374d", "66436266589c7f8d6cbc374c"]}], "type": "ACTIVE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Product > Products"], "summary": "Product listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1131"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "Sea"}, {"name": "withV<PERSON>ts", "in": "query", "schema": {"type": "boolean"}, "example": "true"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/favoriteProduct": {"post": {"tags": ["Product > Favorite products"], "summary": "Favorite/Unfavorite products", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1118, "ids": ["6645b3b6abe49ad250e0bca9", "66436266589c7f8d6cbc3745"], "type": "FAVORITE"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}, "get": {"tags": ["Product > Favorite products"], "summary": "Favorite product listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1119"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "100"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/search": {"get": {"tags": ["Product"], "summary": "Products List By ( Auto complete / Search / Filter( New ) )", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "deviceaccesstype", "in": "header", "schema": {"type": "string"}, "example": "{{deviceaccesstype}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1170"}, {"name": "searchType", "in": "query", "schema": {"type": "string"}, "example": "FILTER"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "4"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "2"}, {"name": "priceListId", "in": "query", "schema": {"type": "string"}, "example": "671b7806f2a0800aeb6418ef"}, {"name": "branchId", "in": "query", "schema": {"type": "string"}, "example": "671b72dadf056899fc2f479b"}, {"name": "hideOutOfStock", "in": "query", "schema": {"type": "boolean"}, "example": "true"}, {"name": "filters", "in": "query", "schema": {"type": "string"}, "example": "%7B%22productType%22%3A%5B%22RESTOCKED_PRODUCTS%22%5D%7D"}, {"name": "isProductSplitting", "in": "query", "schema": {"type": "boolean"}, "description": "Is temporary falg, will be removed soon", "example": "true"}, {"name": "salesPersonUserRoleId", "in": "query", "schema": {"type": "string"}, "example": "671b75e78a561a09ff51a450"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/existItemNumber": {"get": {"tags": ["Product"], "summary": "Check existing item number", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "itemNumber", "in": "query", "schema": {"type": "string"}, "example": "Y01"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1113"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/rewardProductList": {"get": {"tags": ["Product"], "summary": "Reward product listing", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "string"}, "example": "{{tenantId}}"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/inventoryProductList": {"get": {"tags": ["Product"], "summary": "Inventory listing API", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshtoken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1118"}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": "1"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "100"}, {"name": "search<PERSON>ey", "in": "query", "schema": {"type": "string"}, "example": "SRed"}, {"name": "branchId", "in": "query", "schema": {"type": "string"}, "example": "633bfcc0d80b7f00129952ec"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/updateVariantGroupInfo": {"put": {"tags": ["Product"], "summary": "Update variant group of Parent", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1170, "productId": "677d397fffcaecfd239ecae1", "variants": ["10inch", "4inch", "5inch"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/changeVariantOrder": {"put": {"tags": ["Product"], "summary": "Change Variant Order", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1119, "productVariantIds": ["643f8ab895804bc337558bfd", "643f8ab495804bc337558bfc"]}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/variantType": {"put": {"tags": ["Product"], "summary": "Update Variant Type", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1241, "productId": "6572b154ac7f5859a950e729", "variantTypeId": "6572b154ac7f5859a950e72d", "name": "Dark Black"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/productCount": {"get": {"tags": ["Product"], "summary": "Product Count", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "statusType", "in": "query", "schema": {"type": "string"}, "example": "ACTIVE"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/barcodeDetails": {"get": {"tags": ["Product"], "summary": "Barcode Details", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1114"}, {"name": "barcode", "in": "query", "schema": {"type": "integer"}, "example": "89897654"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/tags": {"get": {"tags": ["Product"], "summary": "Tag list", "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}, {"name": "tenantId", "in": "query", "schema": {"type": "integer"}, "example": "1004"}, {"name": "perPage", "in": "query", "schema": {"type": "integer"}, "example": "10"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/productV2/changeProductType": {"put": {"tags": ["Product"], "summary": "Change Product Type", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"tenantId": 1131, "typeChangeTo": "SINGLE", "productVariantId": "65f99e6a5a8c82581bf15369"}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/productService/report/": {"post": {"tags": ["General"], "summary": "Reports", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "example": {"reportType": "SHIPPING_LABEL", "reportData": {"logo": "https://hawak-prod-public.s3.me-south-1.amazonaws.com/hawak-prod/shipping-label/1004/basketball-player-pd-freesvg.org.svg", "orderId": "100183", "date": "27/02/2025", "numberOfCopies": 2, "from": {"tenantLegalName": "OpenXcell Inc", "streetAddress": "Baleshwar Avenue, 202-203, Sarkhej - Gandhinagar Hwy, opposite Rajpath Rangoli Road, Bodakdev", "region": "Al Madinah Province", "city": "Yanbu Al Bahr", "mobileNumber": "81416 00838", "phoneNumber": "************"}, "to": {"customerLegalName": "Bhargav", "shippingRegion": "Riyadh Province", "shippingCity": "Riyadh", "shippingMobileNumber": "50 328 8268"}}}}}}}, "parameters": [{"name": "Authorization", "in": "header", "schema": {"type": "string"}, "example": "{{Authorization}}"}, {"name": "devicetoken", "in": "header", "schema": {"type": "string"}, "example": "{{<PERSON><PERSON>en}}"}, {"name": "refreshToken", "in": "header", "schema": {"type": "string"}, "example": "{{refreshToken}}"}, {"name": "userroleid", "in": "header", "schema": {"type": "string"}, "example": "{{userRoleId}}"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}}}