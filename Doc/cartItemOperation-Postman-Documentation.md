# Cart Item Operation API Documentation

## Endpoint Overview
The `cartItemOperation` endpoint allows you to perform bulk operations (create, update, delete) on cart items in the system. This endpoint is designed for data synchronization purposes and supports batch processing of multiple cart items.

## API Details

**Endpoint:** `/dataSync/cartItems`
**Method:** `POST`
**Content-Type:** `application/json`

## Authentication & Permissions

### Required Headers
```
authorization: Bearer <your_access_token>
deviceToken: <your_device_token>
userroleid: <your_user_role_id>
```


## Request Body Structure

The request body should be an array of cart item objects. Each cart item must contain the following required fields:

### Required Fields
- `_id` - Unique identifier for the cart item
- `tenant_id` - Tenant identifier (integer, min: 1000)
- `customer_user_role_id` - Customer user role ID
- `sales_user_role_id` - Sales user role ID
- `product_variant_id` - Product variant ID
- `quantity` - Quantity of the item
- `last_cart_action` - Last action performed on the cart item
- `item_number` - Item number/sku
- `product_name` - Name of the product
- `master_price_id` - Master price ID
- `base_price` - Base price of the item
- `tax` - Tax amount
- `uom_id` - Unit of measure ID
- `uom_name` - Unit of measure name
- `min_qty` - Minimum quantity
- `action` - Operation to perform: `create`, `update`, or `delete`

### Optional Fields
- `updated_at` - Timestamp of last update (used for conflict resolution)

## Request Examples

### 1.  Mixed Operations (Create, Update, Delete)

```json
POST /productService/dataSync/cartItems
Headers:
  authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  deviceToken: device_12345
  userroleid: user_67890

Body:
[
  {
    "_id": "642123d7000daf92980c5b38",
    "last_cart_action": "ADD_ITEM_TO_CART",
    "cart_id": "1114_6413ee63eaf2900012533507_636b90ca5fb42c001210d16e",
    "tenant_id": 1114,
    "product_variant_id": "63aea629354db1001215466b",
    "item_number": "101",
    "product_name": "Heater",
    "product_secondary_name": "pp",
    "master_price_id": "637c687c4f63130012f43d15",
    "base_price": 440,
    "tax": 64,
    "quantity": 4,
    "min_qty": 4,
    "uom_id": "637c9d7884b9540012d6aadc",
    "uom_name": "A",
    "item_comment": [],
    "customer_user_role_id": "6413ee63eaf2900012533507",
    "sales_user_role_id": "63bd0fa3282c53001268f434",
    "tax_calculation_info": {
      "tax_id": "639987c6281ecb0012eb2232",
      "type": "GROUP",
      "tax_name": "CGST + SGST ",
      "calculated_tax": 64,
      "group_taxes": {
        "63bbb1372aea3a5f887b4d0e": {
          "tax_id": "63bbb1372aea3a5f887b4d0e",
          "tax_name": "CGST",
          "tax_rate": 10,
          "calculated_tax": 44,
          "tax_calculation": "PERCENTAGE"
        },
        "63bbb1372aea3a5f887b4d10": {
          "tax_id": "63bbb1372aea3a5f887b4d10",
          "tax_name": "SGST",
          "tax_rate": 20,
          "calculated_tax": 20,
          "tax_calculation": "FLAT_VALUE"
        }
      },
      "price": "EXCLUDE"
    },
    "original_price": 440,
    "created_by": "62e275ac0b77a6bbdf16fac8",
    "update_by": "62e275ac0b77a6bbdf16fac8",
    "created_at": "2023-03-27T05:04:23.193Z",
    "updated_at": "2023-03-27T05:04:23.193Z",
    "action": "update"
  },
  {
    "_id": "641d986b000daf9298f39712",
    "last_cart_action": "ADD_ITEM_TO_CART",
    "cart_id": "1114_639c2d6d5359ee0012333664_634d018c98a6bf0012d2303a",
    "tenant_id": 1114,
    "product_variant_id": "63aea629354db1001215466b",
    "item_number": "101",
    "product_name": "Heater",
    "product_secondary_name": "pp",
    "master_price_id": "637c687c4f63130012f43d15",
    "base_price": 440,
    "tax": 64,
    "quantity": 4,
    "min_qty": 4,
    "uom_id": "637c9d7884b9540012d6aadc",
    "uom_name": "A",
    "item_comment": [],
    "customer_user_role_id": "639c2d6d5359ee0012333664",
    "sales_user_role_id": "63bd0fa3282c53001268f434",
    "tax_calculation_info": {
      "tax_id": "639987c6281ecb0012eb2232",
      "type": "GROUP",
      "tax_name": "CGST + SGST ",
      "calculated_tax": 64,
      "group_taxes": {
        "63bbb1372aea3a5f887b4d0e": {
          "tax_id": "63bbb1372aea3a5f887b4d0e",
          "tax_name": "CGST",
          "tax_rate": 10,
          "calculated_tax": 44,
          "tax_calculation": "PERCENTAGE"
        },
        "63bbb1372aea3a5f887b4d10": {
          "tax_id": "63bbb1372aea3a5f887b4d10",
          "tax_name": "SGST",
          "tax_rate": 20,
          "calculated_tax": 20,
          "tax_calculation": "FLAT_VALUE"
        }
      },
      "price": "EXCLUDE"
    },
    "original_price": 440,
    "created_by": "633bcd714018180011efc6be",
    "update_by": "633bcd714018180011efc6be",
    "created_at": "2023-03-24T12:32:42.992Z",
    "updated_at": "2023-03-24T12:32:42.992Z",
    "action": "update"
  },
  {
    "_id": "68a845d346bb1b201445e78d" ,
    "customer_user_role_id": "63bb9de3b1d22300110211d6" ,
    "product_variant_id": "63aea629354db1001215466b" ,
    "cart_id": "1114_63bb9de3b1d22300110211d6_63e0d9761405d20012605144",
    "base_price": 344.091,
    "created_at":"2025-08-22T10:26:27.351Z" ,
    "created_by": "63245323654e2a0074f130b5" ,
    "deal_info": {
      "deal_id": "68a84591b92ca5b99a84539a",
      "deal_name": "check deal",
      "deal_number": "DN10024",
      "deal_type": "DISCOUNT",
      "deal_from_date":"2025-08-22T10:25:21.700Z" ,
      "deal_to_date":"2025-08-23T18:29:59.595Z" ,
      "secondary_deal_name": "bul pricing",
      "deal_product_id": "68a845b1b92ca5b99a8453b8" ,
      "discount_type": "PERCENT",
      "percent": 10,
      "amount": 44.5,
      "discounted_price": 400.5
    },
    "item_comment": [],
    "item_number": "101",
    "last_cart_action": "ADD_ITEM_TO_CART",
    "master_price_id": "637c687c4f63130012f43d15" ,
    "min_qty": 4,
    "original_price": 445,
    "product_name": "Heater edited",
    "product_secondary_name": "pp",
    "quantity": 4,
    "sales_user_role_id": "63e0d9761405d20012605144" ,
    "tax": 56.409,
    "tax_calculation_info": {
      "tax_id": "639987c6281ecb0012eb2232" ,
      "type": "GROUP",
      "tax_name": "CGST + SGST ",
      "calculated_tax": 56.409,
      "group_taxes": {
        "63bbb1372aea3a5f887b4d10": {
          "tax_id": "63bbb1372aea3a5f887b4d10" ,
          "tax_name": "SGST",
          "tax_rate": 20,
          "calculated_tax": 20,
          "tax_calculation": "FLAT_VALUE"
        },
        "63bbb1372aea3a5f887b4d0e": {
          "tax_id": "63bbb1372aea3a5f887b4d0e" ,
          "tax_name": "CGST",
          "tax_rate": 10,
          "calculated_tax": 36.409,
          "tax_calculation": "PERCENTAGE"
        }
      },
      "price": "INCLUDE"
    },
    "tenant_id": 1114,
    "uom_id": "637c9d7884b9540012d6aadc" ,
    "uom_name": "A",
    "update_by": "63245323654e2a0074f130b5" ,
    "updated_at":"2025-08-22T10:26:27.351Z" ,
    "action": "create"
  }
]

``

## Response Format

### Success Response
```json
{
  "status": "success",
  "message": null,
  "data": {
    "processed": 3,
    "skipped": 0,
    "created": 1,
    "updated": 1,
    "deleted": 1,
    "errors": []
  }
}
```

### Error Response
```json
{
  "status": "error",
  "message": "Validation failed",
  "data": {
    "processed": 0,
    "skipped": 2,
    "created": 0,
    "updated": 0,
    "deleted": 0,
    "errors": [
      "Validation failed: 2 items have missing required fields",
      "Format errors: 1 items have invalid format"
    ],
    "validationDetails": {
      "totalItems": 3,
      "validItems": 0,
      "invalidItems": 3,
      "missingFieldErrors": [
        {
          "itemId": "507f1f77bcf86cd799439011",
          "itemNumber": "PROD-001",
          "missingFields": ["product_name", "tax"],
          "receivedFields": ["_id", "tenant_id", "quantity"],
          "requiredFields": ["_id", "tenant_id", "customer_user_role_id", "sales_user_role_id", "product_variant_id", "quantity", "last_cart_action", "item_number", "product_name", "master_price_id", "base_price", "tax", "uom_id", "uom_name", "min_qty"],
          "itemData": {
            "_id": "507f1f77bcf86cd799439011",
            "tenant_id": 1001,
            "quantity": 5
          }
        }
      ],
      "invalidFormatErrors": [
        {
          "itemId": "unknown",
          "itemNumber": "unknown",
          "error": "Invalid cart item format - not an object",
          "receivedValue": null
        }
      ]
    }
  }
}
```

## Response Fields Explanation

- **processed**: Total number of items processed
- **skipped**: Number of items skipped due to validation errors or conflicts
- **created**: Number of items successfully created
- **updated**: Number of items successfully updated
- **deleted**: Number of items successfully deleted
- **errors**: Array of error messages for failed operations
- **validationDetails**: Detailed validation information including:
  - **totalItems**: Total number of items in the request
  - **validItems**: Number of items that passed validation
  - **invalidItems**: Number of items that failed validation
  - **missingFieldErrors**: Array of detailed missing field errors
  - **invalidFormatErrors**: Array of format validation errors

## Business Logic

### Conflict Resolution
- If an item with the same key exists and has a newer timestamp, the operation will be skipped
- The system uses the following fields to create a unique key: `tenant_id`, `sales_user_role_id`, `customer_user_role_id`, `product_variant_id`, `item_number`

### Batch Processing
- Items are processed in chunks of 20 for optimal performance
- Each chunk is processed independently, so errors in one chunk don't affect others

### Operation Determination
- **CREATE**: Creates new cart items or replaces existing ones if they have older timestamps
- **UPDATE**: Updates existing items or creates new ones if they don't exist
- **DELETE**: Removes items from the cart

## Error Handling

### Common Error Scenarios
1. **Missing Required Fields**: Items with missing required fields are skipped
2. **Invalid Data Types**: Items with invalid data types are skipped
3. **Permission Denied**: Returns 403 Forbidden if user lacks required permissions
4. **Authentication Failed**: Returns 401 Unauthorized if authentication fails
5. **Validation Errors**: Returns 422 Unprocessable Entity for validation failures

### Detailed Error Information
The API now provides comprehensive error details in the `validationDetails` object:

#### Missing Field Errors
Each missing field error includes:
- `itemId`: The ID of the cart item (or 'unknown' if not available)
- `itemNumber`: The item number/sku (or 'unknown' if not available)
- `missingFields`: Array of field names that are missing
- `receivedFields`: Array of field names that were actually received
- `requiredFields`: Array of all required field names
- `itemData`: The actual data received for the item

#### Format Errors
Each format error includes:
- `itemId`: The ID of the cart item (or 'unknown' if not available)
- `itemNumber`: The item number/sku (or 'unknown' if not available)
- `error`: Description of the format error
- `receivedValue`: The actual value received

### Error Response Codes
- `400` - Bad Request (malformed request)
- `401` - Unauthorized (missing or invalid authentication)
- `403` - Forbidden (insufficient permissions)
- `422` - Unprocessable Entity (validation errors)
- `500` - Internal Server Error (server-side errors)

## Best Practices

1. **Batch Size**: Keep batch sizes reasonable (20-50 items) for optimal performance
2. **Error Handling**: Always check the response for errors and handle them appropriately
3. **Timestamp Management**: Ensure `updated_at` timestamps are accurate to avoid conflicts
4. **Validation**: Validate data on the client side before sending to reduce skipped items
5. **Retry Logic**: Implement retry logic for failed operations
6. **Monitoring**: Monitor the `skipped` count to identify data quality issues

## Rate Limiting

The API processes items in chunks of 20 to maintain performance. Large batches are automatically split into smaller chunks for processing.

