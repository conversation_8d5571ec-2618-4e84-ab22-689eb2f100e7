/**
 * @description This script will populate cart_items_2.0 collection with realistic test data
 * for multiple customers using real products from the database.
 * 
 * Features:
 * - Fetches real products of type SINGLE or VARIANT
 * - Creates separate cart items for different customers
 * - Calculates proper tax amounts and deal information
 * - Generates random quantities between 1-100 per item
 * - Ensures all required CartItemSchema fields are populated
 */

const mongoose = require("mongoose");
const { CART_ACTIONS } = require("../Configs/constants");

// Import models
const ProductModel = new (require("../Models/ProductModel"));
const CartItemModel = require("../Database/Schemas/order/cart_items");
const OrderModel = new (require("../Models/OrderModel"));
const DealModel = new (require("../Models/DealModel"));
const InternalServiceModel = new (require("../Models/InternalServiceModel"));

const CONFIG = {
    TENANT_ID: 1131,
    BATCH_SIZE: 100,
    CUSTOMERS_COUNT: 15,
    SALES_USERS_COUNT: 3,
    MIN_CART_ITEMS_PER_CUSTOMER: 50,
    MAX_CART_ITEMS_PER_CUSTOMER: 800
};

/**
 * Generate unique cart IDs for each customer with sales person
 */
const generateCartIds = (customerUserRoleIds, salesUserRoleIds) => {
    return customerUserRoleIds.map((customerId, index) => {
        const salesUserId = salesUserRoleIds[index % salesUserRoleIds.length];
        return `${CONFIG.TENANT_ID}_${customerId.toString()}_${salesUserId.toString()}`;
    });
};

/**
 * Calculate tax amount based on base price and tax rate
 */
const calculateTax = (basePrice, taxRate = 0.18) => {
    return Number((basePrice * taxRate).toFixed(2));
};

/**
 * Get random element from array
 */
const getRandomElement = (array) => {
    return array[Math.floor(Math.random() * array.length)];
};

/**
 * Generate random quantity that respects minimum quantity constraints
 * @param {number} minQty - Minimum quantity required for the product
 * @param {number} maxQty - Maximum quantity to generate (default: 100)
 * @returns {number} Valid quantity that's >= minQty and is a multiple of minQty
 */
const getRandomQuantity = (minQty = 1, maxQty = 100) => {
    // Ensure minQty is at least 1
    const validMinQty = Math.max(minQty || 1, 1);
    
    // Calculate how many multiples of minQty we can fit within maxQty
    const maxMultiples = Math.floor(maxQty / validMinQty);
    
    // Generate a random multiplier (at least 1)
    const multiplier = Math.floor(Math.random() * Math.max(maxMultiples, 1)) + 1;
    
    // Return quantity as multiple of minQty
    return validMinQty * multiplier;
};

/**
 * Main script function to populate cart items
 */
scripts = async (req, res) => {
    try {
        console.log("🚀 Starting cart items population...");

        // Fetch real customers and sales persons from database
        console.log("👥 Fetching customers and sales persons...");
        
        const [customers, salesPersons] = await Promise.all([
            // Fetch customers
            InternalServiceModel.getCustomers({
                filter: {
                    tenant_id: CONFIG.TENANT_ID,
                    is_deleted: false,
                    is_active: true
                },
                projection: "_id customer_name",
                options: {
                    lean: true,
                    limit: CONFIG.CUSTOMERS_COUNT,
                    sort: { created_at: -1 }
                }
            }),
            // Fetch sales persons
            InternalServiceModel.getSalesPersons({
                filter: {
                    tenant_id: CONFIG.TENANT_ID,
                    is_deleted: false
                },
                projection: "_id",
                options: {
                    lean: true,
                    limit: CONFIG.SALES_USERS_COUNT,
                    sort: { created_at: -1 }
                }
            })
        ]);

        if (!customers.length) {
            const message = "No customers found for tenant";
            console.error(`❌ ${message}`);
            if (res && res.handler) {
                return res.handler.notFound(message);
            }
            throw new Error(message);
        }

        if (!salesPersons.length) {
            const message = "No sales persons found for tenant";
            console.error(`❌ ${message}`);
            if (res && res.handler) {
                return res.handler.notFound(message);
            }
            throw new Error(message);
        }

        console.log(`✅ Found ${customers.length} customers and ${salesPersons.length} sales persons`);

        // Validate that we have sufficient users for cart generation
        if (customers.length < CONFIG.CUSTOMERS_COUNT) {
            console.warn(`⚠️ Only ${customers.length} customers found, but ${CONFIG.CUSTOMERS_COUNT} requested. Adjusting customer count.`);
        }

        if (salesPersons.length < CONFIG.SALES_USERS_COUNT) {
            console.warn(`⚠️ Only ${salesPersons.length} sales persons found, but ${CONFIG.SALES_USERS_COUNT} requested. Will cycle through available sales persons.`);
        }

        const customerUserRoleIds = customers.map(customer => customer._id);
        const salesUserRoleIds = salesPersons.map(sp => sp._id);

        // Validate that all IDs are valid ObjectIds
        const invalidCustomerIds = customerUserRoleIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
        const invalidSalesIds = salesUserRoleIds.filter(id => !mongoose.Types.ObjectId.isValid(id));

        if (invalidCustomerIds.length > 0) {
            throw new Error(`Invalid customer IDs found: ${invalidCustomerIds.join(', ')}`);
        }

        if (invalidSalesIds.length > 0) {
            throw new Error(`Invalid sales person IDs found: ${invalidSalesIds.join(', ')}`);
        }

        // 1. Fetch eligible products (SINGLE or VARIANT type)
        console.log("📦 Fetching products...");
        const products = await ProductModel.findProducts(
            {
                tenant_id: CONFIG.TENANT_ID,
                type: { $in: ["SINGLE", "VARIANT"] }, // Explicitly exclude PARENT type
                is_active: true,
                is_deleted: false,
                "price_mappings.0": { $exists: true },
                "price_mappings.price": { $gt: 0 }
            },
            {
                _id: 1,
                variant_id: 1,
                item_number: 1,
                parent_item_number: 1,
                title: 1,
                secondary_language_title: 1,
                product_name: 1,
                variant_name: 1,
                variant_secondary_name: 1,
                group_name: 1,
                group_secondary_name: 1,
                price_mappings: 1,
                packaging_map: 1,
                tax_id: 1,
                type: 1,
                parent_id: 1,
                variant_value_id: 1,
                group_value_id: 1
            },
            {
                populate: [
                    {
                        path: "tax_id",
                        select: "tax_rate tax_name type tax_calculation"
                    },
                    {
                        path: "packaging_map.uom_id",
                        select: "unit_name unit_short_name"
                    },
                    {
                        path: "parent_id",
                        select: "title item_number secondary_language_title"
                    },
                    {
                        path: "variant_value_id",
                        select: "name secondary_language_name"
                    },
                    {
                        path: "group_value_id",
                        select: "name secondary_language_name"
                    }
                ],
                lean: true
            }
        );

        if (!products.length) {
            const message = "No eligible products found";
            console.error(`❌ ${message}`);
            if (res && res.handler) {
                return res.handler.notFound(message);
            }
            throw new Error(message);
        }

        console.log(`✅ Found ${products.length} eligible products`);

        // 2. Fetch deals for products with deal info
        const productIds = products.map(p => p._id);
        const dealProducts = await DealModel.findDealProducts(
            {
                product_id: { $in: productIds },
                is_active: true
            },
            {
                _id: 1,
                product_id: 1,
                deal_id: 1,
                discount_type: 1,
                percent: 1,
                amount: 1,
                discounted_price: 1,
                deal_from_date: 1,
                deal_to_date: 1
            },
            { 
                lean: true,
                populate: [
                    {
                        path: "deal_id",
                        select: "deal_name secondary_deal_name deal_id deal_type deal_from_date deal_to_date"
                    }
                ]
            }
        );

        const dealMap = new Map();
        dealProducts.forEach(deal => {
            dealMap.set(deal.product_id.toString(), deal);
        });

        // 3. Generate cart IDs with consistent customer-sales person assignments
        const cartIds = generateCartIds(customerUserRoleIds, salesUserRoleIds);
        
        // Create a mapping of customer to their assigned sales person for consistency
        const customerSalesPersonMap = new Map();
        customerUserRoleIds.forEach((customerId, index) => {
            const assignedSalesUserId = salesUserRoleIds[index % salesUserRoleIds.length];
            customerSalesPersonMap.set(customerId.toString(), assignedSalesUserId);
        });

        // 4. Create cart items
        console.log("🛒 Generating cart items...");
        const cartItems = [];
        let totalItemsGenerated = 0;
        let successfulItemsCount = 0;
        let errorItemsCount = 0;

        for (let customerIndex = 0; customerIndex < Math.min(CONFIG.CUSTOMERS_COUNT, customerUserRoleIds.length); customerIndex++) {
            const customerId = customerUserRoleIds[customerIndex];
            const cartId = cartIds[customerIndex];
            // Use the consistently assigned sales person instead of random selection
            const salesUserId = customerSalesPersonMap.get(customerId.toString());
            
            const itemsCount = Math.floor(
                Math.random() * (CONFIG.MAX_CART_ITEMS_PER_CUSTOMER - CONFIG.MIN_CART_ITEMS_PER_CUSTOMER + 1)
            ) + CONFIG.MIN_CART_ITEMS_PER_CUSTOMER;

            console.log(`👤 Generating ${itemsCount} cart items for customer ${customerIndex + 1}/${Math.min(CONFIG.CUSTOMERS_COUNT, customerUserRoleIds.length)} (Sales Person: ${salesUserId})`);

            // Select random products for this customer
            const selectedProducts = [];
            for (let i = 0; i < itemsCount; i++) {
                selectedProducts.push(getRandomElement(products));
            }

            for (const product of selectedProducts) {
                try {
                    // Validate product type - ensure only SINGLE or VARIANT products are processed
                    if (product.type === "PARENT") {
                        console.warn(`⚠️ Skipping PARENT product ${product.item_number} - Parent products are not purchasable`);
                        errorItemsCount++;
                        continue;
                    }

                    if (!["SINGLE", "VARIANT"].includes(product.type)) {
                        console.warn(`⚠️ Invalid product type ${product.type} for product ${product.item_number}, skipping...`);
                        errorItemsCount++;
                        continue;
                    }

                    // Validate product has required data
                    if (!product.price_mappings || !product.price_mappings.length) {
                        console.warn(`⚠️ Product ${product.item_number} has no price mappings, skipping...`);
                        errorItemsCount++;
                        continue;
                    }

                    // Get price mapping
                    const priceMapping = product.price_mappings[0];
                    if (!priceMapping || !priceMapping.price || priceMapping.price <= 0) {
                        console.warn(`⚠️ Product ${product.item_number} has invalid price mapping, skipping...`);
                        errorItemsCount++;
                        continue;
                    }

                    const basePrice = priceMapping.price;
                    const masterPriceId = priceMapping.master_price_id;

                    // Get packaging info with dynamic UOM data
                    const minQty = product.packaging_map?.min_qty || 1;
                    const uomId = product.packaging_map?.uom_id?._id || product.packaging_map?.uom_id || new mongoose.Types.ObjectId();
                    const uomName = product.packaging_map?.uom_id?.unit_short_name || 
                                   product.packaging_map?.uom_id?.unit_name || 
                                   product.packaging_map?.uom_name || 
                                   "UNIT";

                    // Generate quantity that respects minimum quantity constraints
                    const quantity = getRandomQuantity(minQty, 100);

                    // Calculate tax with dynamic tax information
                    const taxRate = product.tax_id?.tax_rate || 0;
                    const taxName = product.tax_id?.tax_name || "No Tax Applied";
                    const taxType = product.tax_id?.type || "SINGLE";
                    const taxCalculation = product.tax_id?.tax_calculation || (taxRate > 0 ? "PERCENTAGE" : "FLAT_VALUE");
                    const tax = taxRate > 0 ? calculateTax(basePrice, taxRate) : 0;

                    // Check for deal info
                    const dealInfo = dealMap.get(product._id.toString());

                    // Get parent details for proper naming hierarchy
                    const parentDetails = product.parent_id;

                    // Generate cart item with dynamic values following OrderController patterns
                    const cartItem = {
                        last_cart_action: CART_ACTIONS.ADD_ITEM_TO_CART,
                        cart_id: `${CONFIG.TENANT_ID}_${customerId}_${salesUserId}`,
                        tenant_id: CONFIG.TENANT_ID,
                        product_variant_id: product._id,
                        variant_id: product.variant_id || null,
                        item_number: product.item_number,
                        parent_item_number: parentDetails?.item_number || product.parent_item_number || null,
                        product_name: product.title || parentDetails?.title || product.product_name || product.item_number,
                        product_secondary_name: product.secondary_language_title || parentDetails?.secondary_language_title || null,
                        variant_name: product.variant_value_id?.name || product.variant_name || null,
                        variant_secondary_name: product.variant_value_id?.secondary_language_name || product.variant_secondary_name || null,
                        group_name: product.group_value_id?.name || product.group_name || null,
                        group_secondary_name: product.group_value_id?.secondary_language_name || product.group_secondary_name || null,
                        master_price_id: masterPriceId,
                        base_price: basePrice,
                        tax: tax,
                        quantity: quantity,
                        min_qty: minQty,
                        uom_id: uomId,
                        uom_name: uomName,
                        item_comment: [],
                        customer_user_role_id: customerId,
                        sales_user_role_id: salesUserId,
                        tax_calculation_info: {
                            tax_id: product.tax_id?._id || new mongoose.Types.ObjectId(),
                            type: taxType,
                            tax_name: taxName,
                            tax_rate: taxRate,
                            tax_calculation: taxCalculation,
                            calculated_tax: tax,
                            price: "INCLUDE"
                        },
                        original_price: basePrice,
                        created_by: salesUserId,
                        update_by: salesUserId
                    };

                    // Add deal info only if deal exists and has all required data
                    if (dealInfo && dealInfo.deal_id) {
                        const dealData = dealInfo.deal_id;
                        cartItem.deal_info = {
                            deal_id: dealData._id || dealData,
                            deal_name: dealData.deal_name || `Deal for ${product.item_number}`,
                            secondary_deal_name: dealData.secondary_deal_name || null,
                            deal_number: dealData.deal_id || `${CONFIG.TENANT_ID}_DEAL_${Date.now()}`,
                            deal_type: dealData.deal_type || dealInfo.discount_type || "DISCOUNT",
                            deal_from_date: dealData.deal_from_date || dealInfo.deal_from_date || new Date(),
                            deal_to_date: dealData.deal_to_date || dealInfo.deal_to_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                            deal_product_id: dealInfo._id,
                            discount_type: dealInfo.discount_type,
                            percent: dealInfo.percent || null,
                            amount: dealInfo.amount || null,
                            discounted_price: dealInfo.discounted_price || null
                        };
                    }

                    cartItems.push(cartItem);
                    totalItemsGenerated++;
                    successfulItemsCount++;

                } catch (error) {
                    console.error(`❌ Error processing product ${product._id}:`, error.message);
                    errorItemsCount++;
                }
            }
        }

        console.log(`📊 Generated ${totalItemsGenerated} cart items for ${CONFIG.CUSTOMERS_COUNT} customers`);

        // 5. Insert cart items in batches
        console.log("💾 Inserting cart items into database...");
        let insertedCount = 0;
        const insertResults = [];

        for (let i = 0; i < cartItems.length; i += CONFIG.BATCH_SIZE) {
            const batch = cartItems.slice(i, i + CONFIG.BATCH_SIZE);
            
            try {
                const result = await OrderModel.createCartItems(batch);
                insertedCount += result.length;
                insertResults.push({
                    batch: Math.ceil((i + 1) / CONFIG.BATCH_SIZE),
                    inserted: result.length,
                    status: "success"
                });
                console.log(`✅ Inserted batch ${Math.ceil((i + 1) / CONFIG.BATCH_SIZE)} - Total: ${insertedCount}/${cartItems.length}`);
            } catch (error) {
                console.error(`❌ Error inserting batch ${Math.ceil((i + 1) / CONFIG.BATCH_SIZE)}:`, error.message);
                insertResults.push({
                    batch: Math.ceil((i + 1) / CONFIG.BATCH_SIZE),
                    inserted: 0,
                    status: "error",
                    error: error.message
                });
            }
        }

        // 6. Generate summary
        const summary = {
            totalCartItems: insertedCount,
            customersCount: CONFIG.CUSTOMERS_COUNT,
            averageItemsPerCustomer: Math.round(insertedCount / CONFIG.CUSTOMERS_COUNT),
            successfulItemsCount,
            errorItemsCount,
            cartIds: cartIds,
            customerUserRoleIds: customerUserRoleIds.map(id => id.toString()),
            salesUserRoleIds: salesUserRoleIds.map(id => id.toString()),
            insertResults
        };

        console.log("🎉 Cart items population completed!");

        if (res && res.handler) {
            return res.handler.success(null, summary);
        } else {
            console.log("✅ Summary:", JSON.stringify(summary, null, 2));
            return summary;
        }

    } catch (error) {
        console.error("💥 Error populating cart items:", error);
        if (res && res.handler) {
            return res.handler.serverError(error);
        } else {
            throw error;
        }
    }
};

/**
 * Alternative script function for cleanup - removes all cart items for the tenant
 */
const cleanupCartItems = async (req, res) => {
    try {
        console.log("🧹 Starting cart items cleanup...");

        const result = await CartItemModel.deleteMany({
            tenant_id: CONFIG.TENANT_ID
        });

        console.log(`✅ Deleted ${result.deletedCount} cart items`);

        if (res && res.handler) {
            return res.handler.success(null, {
                deletedCount: result.deletedCount,
                tenantId: CONFIG.TENANT_ID
            });
        } else {
            console.log("✅ Cleanup completed:", {
                deletedCount: result.deletedCount,
                tenantId: CONFIG.TENANT_ID
            });
            return {
                deletedCount: result.deletedCount,
                tenantId: CONFIG.TENANT_ID
            };
        }

    } catch (error) {
        console.error("💥 Error cleaning up cart items:", error);
        if (res && res.handler) {
            return res.handler.serverError(error);
        } else {
            throw error;
        }
    }
};

module.exports = {
    scripts,
    cleanupCartItems
};
