# Cart Items Sync Analysis API Documentation

## Overview

The `syncCartItemsForCustomer` API endpoint provides a comprehensive **analysis** of cart items synchronization needs between offline and online systems. This API **ONLY COMPARES** data and returns analysis results **WITHOUT PERFORMING** any actual database operations. It's designed for read-only analysis and planning purposes.

## ⚠️ **Important Note: READ-ONLY API**

This API is **NOT** for actual synchronization. It only:
- <PERSON><PERSON><PERSON> request data with database
- Analyzes what operations would be needed
- Returns recommendations
- **NO database modifications are performed**

## API Endpoint

```
POST /dataSync/cartItems/customerSync
```

## Request Format

### Headers
Authorization:{{Authorization}}
devicetoken:{{devicetoken}}
refreshToken:{{refreshToken}}
userroleid:{{userRoleId}}

### Request Body

```json
{
    "tenantId": 1114,
    "customerUserRoleId": "639c2d6d5359ee0012333664",
    "cartId": "1114_639c2d6d5359ee0012333664_634d018c98a6bf0012d2303a",
    "cartItems": [
        {
            "_id": "641d9847000daf9298f360fc",
            "updated_at": "2023-03-24T12:32:07.837Z",
        },
        {
            "_id": "641d9847000daf9298f360fe",
            "updated_at": "2023-03-24T12:32:07.837Z",
        }
        .....
    ]
}
```

### Required Fields

| Field | Type | Description | Validation |
|-------|------|-------------|------------|
| `tenantId` | Integer | Tenant identifier | Must be positive integer |
| `customerUserRoleId` | String | Customer user role ID | Must be valid MongoDB ObjectId |
| `cartId` | String | Cart identifier | Must be non-empty string |
| `cartItems` | Array | Array of cart items | Must contain at least one item |
| `cartItems[]._id` | String | Cart item ID | Must be valid MongoDB ObjectId |
| `cartItems[].updated_at` | String | Last update timestamp | Must be valid ISO 8601 date |



```json
{
    "message": "Cart items analysis completed successfully",
    "analysis": {
        "summary": {
            "totalRequested": 2,
            "totalExisting": 0,
            "wouldCreate": 2,
            "wouldUpdate": 0,
            "wouldDelete": 0,
            "wouldSkip": 0
        },
        "details": {
            "createArray": [
                {
                    "_id": "641d9847000daf9298f360fc",
                    "reason": "Item exists in database but not in request",
                    "existingItem": { /* existing item from database */ }
                }
            ],
            "updateArray": [
                {
                    "_id": "641d9847000daf9298f360fc",
                    "reason": "Item exists in database but not in request",
                    "existingItem": { /* existing item from database */ }
                }
            ],
            "deleteArray": [
                {
                    "_id": "641d9848000daf9298f36214",
                    "reason": "Item exists in request but not in database",
                }
            ],
            "skipArray": []
        },
        "recommendations": {
            "create": "Would create 2 new cart items",
            "update": "No items to update",
            "delete": "No items to delete",
            "skip": "No items to skip"
        }
    }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | Boolean | Overall operation success status |
| `message` | String | Description of what was accomplished |
| `analysis.summary.totalRequested` | Integer | Total items in the request |
| `analysis.summary.totalExisting` | Integer | Total items found in database |
| `analysis.summary.wouldCreate` | Integer | Number of items that would be created |
| `analysis.summary.wouldUpdate` | Integer | Number of items that would be updated |
| `analysis.summary.wouldDelete` | Integer | Number of items that would be deleted |
| `analysis.summary.wouldSkip` | Integer | Number of items that would be skipped |
| `analysis.details.createArray` | Array | Detailed info about items that would be created |
| `analysis.details.updateArray` | Array | Detailed info about items that would be updated |
| `analysis.details.deleteArray` | Array | Detailed info about items that would be deleted |
| `analysis.details.skipArray` | Array | Detailed info about items that would be skipped |
| `analysis.recommendations` | Object | Human-readable recommendations |

