const mongoose = require('mongoose');

const { VALUES } = require('./constants');

const dbName = process.env.DB_NAME
const autoIndex = VALUES.IS_PROD_ENV ? false : true

//BUILD A CONNECTION
mongoose.connect(process.env.MONGODB_URL, {
    dbName,
    autoIndex,
}).then(() => {
    const message = `${dbName} database connected successfully :)`
   // const populateCartItems = require("../scripts/populateCartItems")
   // populateCartItems.cleanupCartItems()
   // populateCartItems.scripts() 
    

    console.log(message)
    logger.info(message)
})
    .catch(err => logger.error(err));

if (VALUES.IS_DEV_ENV) {
    mongoose.set("debug", true);
}

module.exports.mongoose = mongoose
