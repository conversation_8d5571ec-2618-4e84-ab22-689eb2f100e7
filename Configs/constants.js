//GLOBAL STATUS
exports.STATUS_CODES = {
    // 1XX INFORMATIONAL
    CONTINUE: 100,
    SWITCHING_PROTOCOLS: 101,
    PROCESSING: 102,
    EARLY_HINTS: 103,

    // 2XX SUCCESS
    SUCCESS: 200,
    CREATED: 201,
    ACCEPTED: 202,
    NON_AUTHORITATIVE_INFORMATION: 203,
    NO_CONTENT: 204,
    RESET_CONTENT: 205,
    PARTIAL_CONTENT: 206,
    MULTI_STATUS: 207,
    ALREADY_REPORTED: 208,
    IM_USED: 226,

    // 3XX REDIRECTION
    MULTIPLE_CHOICES: 300,
    MOVED_PERMANENTLY: 301,
    FOUND: 302,
    SEE_OTHER: 303,
    NOT_MODIFIED: 304,
    USE_PROXY: 305,
    TEMPORARY_REDIRECT: 307,
    PERMANENT_REDIRECT: 308,

    // 4XX CLIENT ERROR
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    PAYMENT_REQUIRED: 402,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    NOT_ALLOWED: 405,
    NOT_ACCEPTABLE: 406,
    PROXY_AUTHENTICATION_REQUIRED: 407,
    REQUEST_TIMEOUT: 408,
    CONFLICT: 409,
    GONE: 410,
    LENGTH_REQUIRED: 411,
    PRECONDITION_FAILED: 412,
    PAYLOAD_TOO_LARGE: 413,
    URI_TOO_LONG: 414,
    UNSUPPORTED_MEDIA_TYPE: 415,
    RANGE_NOT_SATISFIABLE: 416,
    EXPECTATION_FAILED: 417,
    UNPROCESSABLE_ENTITY: 422,
    VALIDATION_ERROR: 422,
    NOT_VALID_DATA: 422,
    LOCKED: 423,
    FAILED_DEPENDENCY: 424,
    UNORDERED_COLLECTION: 425,
    UPGRADE_REQUIRED: 426,
    PRECONDITION_REQUIRED: 428,
    TOO_MANY_REQUESTS: 429,
    REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
    UNAVAILABLE_FOR_LEGAL_REASONS: 451,

    // 5XX SERVER ERROR
    SERVER_ERROR: 500,
    NOT_IMPLEMENTED: 501,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504,
    HTTP_VERSION_NOT_SUPPORTED: 505,
    VARIANT_ALSO_NEGOTIATES: 506,
    INSUFFICIENT_STORAGE: 507,
    LOOP_DETECTED: 508,
    BANDWIDTH_LIMIT_EXCEEDED: 509,
    NOT_EXTENDED: 510,
    NETWORK_AUTHENTICATION_REQUIRED: 511,

    // MongoDB duplicate key code
    MONGODB_DUPLICATE_KEY_CODE: 11000,
    MONGODB_VALIDATION_ERROR_CODE: 121,
};

exports.REGEX = {
    COUNTRY_CODE: /^\+\d+$/,
    UL_CHARACTERS_ONLY: /^[a-zA-Z]+$/,
    CHARACTERS_DIGITS_ONLY: /^[a-zA-Z0-9_.-]*$/,
};

exports.ENVIRONMENTS = {
    DEVELOPMENT: "development",
    STAGING: "staging",
    PRODUCTION: "production"
}

exports.BUCKET_TYPE = {
    "PRIVATE": "PRIVATE",
    "PUBLIC": "PUBLIC"
};

exports.ACTIVE_STATUS_ENUM = {
    ACTIVE: "ACTIVE",
    "INACTIVE": "INACTIVE"
};

exports.TENANT_ADVANCE_LIMIT_KEYS = {
    'NUMBER_OF_USERS': 'NUMBER_OF_USERS',
    'NUMBER_OF_CUSTOMERS': 'NUMBER_OF_CUSTOMERS',
    'NUMBER_OF_PRODUCTS': 'NUMBER_OF_PRODUCTS',
    'STORAGE_ALLOWANCE': 'STORAGE_ALLOWANCE',
}

exports.PRODUCT_VARIANT_TYPES = {
    GROUP_VALUE: "GROUP_VALUE",
    VARIANT_VALUE: "VARIANT_VALUE"
};

exports.VARIANT_TYPES = {
    GROUP: "GROUP",
    VARIANT: "VARIANT"
}

exports.PRODUCT_LISTING_OPTION = {
    ACTIVE: "ACTIVE",
    INACTIVE: "INACTIVE",
};
exports.NEW_PRODUCT_TYPE = {
    SINGLE: "SINGLE",
    PARENT: "PARENT",
    VARIANT: "VARIANT",
};

exports.PRODUCT_TYPE = {
    SINGLE: "SINGLE",
    VARIANT: "VARIANT",
};

exports.LISTING_PRODUCT_TYPE = {
    SINGLE: this.NEW_PRODUCT_TYPE.SINGLE,
    PARENT: this.NEW_PRODUCT_TYPE.PARENT,
}

exports.SELECTED_ATTRIBUTES_SET_TYPE = {
    MANUAL: "MANUAL",
    SET: "SET"
}

exports.PRODUCT_SEARCH_TYPES = {
    AUTO_COMPLETE: "AUTO_COMPLETE",
    SEARCH: "SEARCH",
    FILTER: "FILTER",
}

exports.PRODUCT_FILTER_TYPES = {
    NEW_PRODUCTS: "NEW_PRODUCTS",
    RESTOCKED_PRODUCTS: "RESTOCKED_PRODUCTS"
}

exports.CART_ACTIONS = {
    ADD_ITEM_TO_CART: "ADD_ITEM_TO_CART",
    UPDATE_QUANTITY: "UPDATE_QUANTITY",
    REMOVE_FROM_CART: "REMOVE_FROM_CART",
    ADD_ITEM_COMMENT: "ADD_ITEM_COMMENT",
    EDIT_ITEM_PRICE: "EDIT_ITEM_PRICE",
    DRAFT_TO_CART: "DRAFT_TO_CART"
};

exports.ORDER_PUNCH_DEVICE_TYPES = {
    TABLET: "TABLET",
    MOBILE: "MOBILE"
};

exports.ORDER_STAT_ROLES = {
    SALESPERSON: "SALESPERSON",
    CUSTOMER: "CUSTOMER"
}

exports.ORDER_PUNCH_DEVICE_OS = {
    IOS: "IOS",
    ANDROID: "ANDROID"
};
exports.DURATION_PERIOD_OPTIONS = {
    months: "months",
    days: "days"
}

exports.ORDER_EXPORT = {
    ORDER_STATUS_LIST: {
        PENDING: "Pending",
        RECEIVED: "Received",
        RELEASED: "Released",
        PREPARING: "Preparing",
        SHIPPED: "Shipped",
        DELIVERED: "Delivered",
        CANCELLED: "Cancelled",
        ON_HOLD: "On Hold"
    },
    STATUS: {
        PENDING: "PENDING",
        IN_PROGRESS: "IN_PROGRESS",
        COMPLETED: "COMPLETED",
        FAILED: "FAILED"
    },
    APP_TYPE: {
        SALES_APP: "Salesperson",
        CUSTOMER_APP: "Customer",
        SUPERVISOR_APP: "Supervisor",
        TENANT_OWNER: "Owner",
        ADMIN: "Admin",
    },
    DEVICE_TYPES: {
        TABLET: "Tablet",
        MOBILE: "Mobile"
    },
    DEVICE_OS: {
        IOS: "iOS",
        ANDROID: "Android"
    }
}

exports.ORDER_STATUS_TYPES = {
    PENDING: "PENDING",
    RECEIVED: "RECEIVED",
    RELEASED: "RELEASED",
    PREPARING: "PREPARING",
    SHIPPED: "SHIPPED",
    DELIVERED: "DELIVERED",
    CANCELLED: "CANCELLED",
    DASHBOARD_ORDERS: "DASHBOARD_ORDERS",
    ON_HOLD: "ON_HOLD"
}

exports.ORDER_APP_TYPE = {
    SALES_APP: "SALES_APP",
    CUSTOMER_APP: "CUSTOMER_APP",
    SUPERVISOR_APP: "SUPERVISOR_APP",
    TENANT_OWNER: "TENANT_OWNER",
    ADMIN: "ADMIN",
};

exports.VALUES = {
    DEVICE_TYPE: {
        ANDROID: "ANDROID",
        IOS: "IOS",
        WEB: "WEB",
        DESKTOP: "DESKTOP"
    },
    sessionStatus: {
        ACTIVE: "ACTIVE",
        CLOSE: "CLOSE",
        EXPIRED: "EXPIRED",
        LOGOUT: "LOGOUT",
    },
    awsUserPoolRegion: process.env.AWS_POOL_REGION,
    awsPublicBucketBaseURL: process.env.AWS_PUBLIC_BUCKET_BASE_URL,
    awsPublicBucketName: process.env.AWS_PUBLIC_BUCKET_NAME,
    awsBucketName: process.env.AWS_BUCKET_FOLDER,
    userPoolData: {
        UserPoolId: process.env.AWS_USER_POOL_ID,
        ClientId: process.env.AWS_USER_POOL_CLIENT_ID,  //demouser-pool
        ClientSecret: process.env.AWS_USER_POOL_CLIENT_SECRET
    },
    userServiceBaseURL: process.env.USER_SERVICE_BASE_URL,
    internalServiceBaseURL: process.env.INTERNAL_SERVICE_BASE_URL,
    jsReportBaseURL: process.env.JS_REPORT_BASE_URL,
    jsReportUsername: process.env.JS_REPORT_USER_NAME,
    jsReportPassword: process.env.JS_REPORT_PASSWORD,
    category: {
        FAMILY: "FAMILY",
        CATEGORY: "CATEGORY",
        SUBCATEGORY: "SUBCATEGORY",
    },
    MASTER_PRICE: {
        INCLUDE: "INCLUDE",
        EXCLUDE: "EXCLUDE",
    },
    TAX_TYPE: {
        SINGLE: "SINGLE",
        GROUP: "GROUP",
        GROUP_SINGLE_VALUE: "GROUP_SINGLE_VALUE"
    },
    TAX_CALCULATION: {
        PERCENTAGE: "PERCENTAGE",
        FLAT_VALUE: "FLAT_VALUE"
    },
    giftIcon: "https://hawak-locales.s3.me-south-1.amazonaws.com/images/gift-black.svg",

    // ENVIRONMENT check
    ENVIRONMENT: process.env.ENVIRONMENT,

    IS_DEV_ENV: process.env.ENVIRONMENT === this.ENVIRONMENTS.DEVELOPMENT,
    IS_STAGING_ENV: process.env.ENVIRONMENT === this.ENVIRONMENTS.STAGING,
    IS_PROD_ENV: process.env.ENVIRONMENT === this.ENVIRONMENTS.PRODUCTION,

    IS_APP_RUNNING_ON_SERVER: process.env.PM2_PROGRAMMATIC ? true : false,
}

exports.SMS_PROVIDER_INFO = {
    UNIFONIC: "UNIFONIC"
},

    exports.BUCKET_TYPE = {
        "PRIVATE": "PRIVATE",
        "PUBLIC": "PUBLIC",
        "LOCALES": "LOCALES"
    }

exports.IMAGE_LISTING = {
    ALL: "ALL",
    NEW: "NEW",
    NEED_IMAGES: "NEED_IMAGES"
}

exports.LISTING_TYPES = {
    ALL: "ALL",
    SEARCH: "SEARCH",
    PAGINATION: "PAGINATION",
    NEW: "NEW"
};

exports.ORDER_ITEM_LISTING_TYPES = {
    ALL: "ALL",
    PAGINATION: "PAGINATION",
}

exports.MEDIA_TYPE = {
    IMAGE: "IMAGE",
    VIDEO: "VIDEO",
};

exports.IMAGE_THUMB_SIZE = {
    HEIGHT: 750,
    WIDTH: 750
};

exports.TAB_IMAGE_THUMB_SIZE = {
    HEIGHT: 641,
    WIDTH: 1007
};

exports.PHONE_IMAGE_THUMB_SIZE = {
    HEIGHT: 200,
    WIDTH: 200
};

exports.EMAIL_VERIFIER_URL = process.env.BASE_URL + "auth/email-verification/"

exports.EMAIL_ID = {
    // System email ids
    NOTIFICATION: "<EMAIL>",

    // System users email ids
    FARIS: "<EMAIL>",
    YOUSEF: "<EMAIL>",
    YASHASHWINI: "<EMAIL>",
    INDRAJEET: "<EMAIL>",
    BRIJESH: "<EMAIL>",
    RAJAN: "<EMAIL>",
    DEEP: "<EMAIL>",
}

const getProductImagePath = (imageType) => (
    `${this.VALUES.awsBucketName}product/${imageType}`
)

exports.FILE_PATH = { // MEDIA PATH FOR AWS S3
    PRODUCT_IMAGE: {
        WEB: getProductImagePath("web"),
        TAB: getProductImagePath("tablet"),
        PHONE: getProductImagePath("mobile"),
        LOGO: getProductImagePath("logo"),
        THUMBNAIL: getProductImagePath("thumbnail"),
        MINI_THUMBNAIL: getProductImagePath("mini_thumbnail"),
    },

    // Data sheet path
    DATA_SHEET: {
        EXPORT: process.env.AWS_BUCKET_FOLDER + "dataSheet",
        PRODUCT_SAMPLE: process.env.AWS_DATA_SHEET_FOLDER + "Product_Import_Template.xlsx",
        PRODUCT_SAMPLE_WITH_GROUP: process.env.AWS_DATA_SHEET_FOLDER + "Product_Import_Template_With_Group.xlsx",
    },
    S3_URL: {
        DATA_SHEET: {
            ORDER_EXPORT: (
                filename,
                tenantId
            ) => {
                return process.env.AWS_PUBLIC_BUCKET_BASE_URL +
                    process.env.AWS_BUCKET_FOLDER +
                    `dataSheet/${tenantId}/order/update/export/` +
                    filename
            }
        },
        CATEGORIES_FAMILY_IMAGES: this.VALUES.awsPublicBucketBaseURL + process.env.AWS_BUCKET_FOLDER + "categories/family/original",
    }
}

exports.PRIMITIVE_ROLES = {
    SYSTEM_OWNER: "System Owner",
    SUPER_ADMIN: "Super Admin",
    ACCOUNT_MANAGER: "Account Manager",
    TENANT_OWNER: "Tenant Owner",
    TENANT_ADMIN: "Admin",
    BRANCH_MANAGER: "Branch Manager",
    SALES_PERSON: "Sales Person",
    SUPERVISOR: "Supervisor",
    CUSTOMER: "Customer",
    WAREHOUSE_CLERK: "Warehouse Clerk",
    ACCOUNTANT: "Accountant",
    CONTRIBUTOR: "Contributor",
};

exports.VARIANT_TYPE_ENUM = {
    COLOR: "Color",
    SIZE: "Size",
    TYPE: "Type",
};

// TODO: Need to allow only .jpeg
exports.VALID_IMAGES_EXTS = [".jpg", ".jpeg", ".png", ".heic", ".heif", ".JPG", ".JPEG", ".PNG", ".HEIC", ".HEIF"]

exports.ENTITY_STATUS = {
    ACTIVE: "ACTIVE",
    INACTIVE: "INACTIVE",
    ALL: "ALL"
}

exports.APP_TYPE = {
    NATIVE: "NATIVE",
    WEB: "WEB"
}

exports.MASTER_DATA_ENTITIES = {
    ATTRIBUTE: "ATTRIBUTE",
    ATTRIBUTE_SET: "ATTRIBUTE_SET",
    BRAND: "BRAND",
    PRICE: "PRICE",
    UNIT: "UNIT",
    ASSOCIATED_ATTRIBUTES: "ASSOCIATED_ATTRIBUTES",
}

exports.FALSELY_VALUES = [0, "", undefined, null, false, NaN, "0", "undefined", "null", "false", "NaN"]

exports.INCREMENT_PRODUCT_NUMBER_BY = 100;

exports.INCREMENT_DEAL_PRODUCT_NUMBER_BY = 100;

exports.TENANT_ALLOW_VALIDATION = [ // Dec 30 product limit validation
    PRODUCT = "PRODUCTS"
] // Dec 30 product limit validation

exports.FAVORITE_PRODUCT_TYPE = {
    FAVORITE: "FAVORITE",
    UNFAVORITE: "UNFAVORITE"
}

exports.PRODUCT_SORT_BY = {
    TITLE: "title",
    ITEM_NUMBER: "item_number"
}

exports.DEAL_SORT_BY = {
    PRICE: "PRICE",
    INVENTORY: "INVENTORY"
}

exports.DEAL_STATISTICS_EVENT_TYPE = {
    CLICK_VIEW_COUNT_UPDATE: "CLICK_VIEW_UPDATE",
    ADD_TO_CART_COUNT_UPDATE: "ADD_TO_CART_COUNT_UPDATE",
    SALES_COUNT_UPDATE: "SALES_COUNT_UPDATE"
}

exports.PRODUCT_SORT_TYPE = {
    ASC: "ASC",
    DESC: "DESC"
}

exports.INVENTORY_PRODUCT_LISTING_SORT_TYPES = {
    INVENTORY: "INVENTORY"
}

exports.IMAGE_TYPE = {
    GROUP_IMAGE: "GROUP_IMAGE"
}

exports.SIGNATURE_IMAGE_TYPE = {
    PRODUCT: "PRODUCT",
    VARIANT: "VARIANT"
}

exports.REPORTS = {
    REPORT_TYPE: {
        SHIPPING_LABEL: "SHIPPING_LABEL",
        ORDER_DETAIL: "ORDER_DETAIL",
    },
    TEMPLATE: {
        SHIPPING_LABEL: "/ShippingLabel/Main.html",
        ORDER_DETAIL: "/OrderDetail/Main.html",
    },
}

exports.DATA_SHEET = {
    DATA_TYPE: {
        PRODUCT: "PRODUCT",
        PRICE: "PRICE",
        INVENTORY: "INVENTORY",
        CUSTOMER: "CUSTOMER"
    },

    OPERATION_TYPE: {
        CREATE: "CREATE",
        UPDATE: "UPDATE",
        CUSTOM_FIELDS: "CUSTOM_FIELDS"
    },

    FILE_TYPE: {
        SIMPLE: "SIMPLE",
        WITH_GROUP: "WITH_GROUP",
    },

    STATUS: {
        COMPLETE: "COMPLETE",
        FOR_REVIEW: "FOR_REVIEW",
        CANCELED: "CANCELED",
        IN_PROGRESS: "IN_PROGRESS"
    },

    SHEET_STATUS: {
        PENDING: "PENDING",
        COMPLETE: "COMPLETE"
    },

    APPROVE_TYPE: {
        ALL: "ALL",
        SELECTED: "SELECTED"
    },

    TYPE: {
        IMPORT: "IMPORT",
        EXPORT: "EXPORT",
    },

    UPDATE_TYPE: {
        DETAILS: "DETAILS",
        STATUS: "STATUS",
    },

    PRODUCT_TYPE: {
        SINGLE: "Single",
        VARIANT: "Variant",
        PARENT: "Parent",
    },

    PARENTAGE_TYPE: {
        COLOR: "color",
        SIZE: "size",
        TYPE: "type",
    },
}

exports.PORTAL_TYPE = {
    TENANT_PORTAL: "TENANT_PORTAL",
    BRANCH_PORTAL: "BRANCH_PORTAL",
    SALES_APP: "SALES_APP",
    CUSTOMER_APP: "CUSTOMER_APP"
}

exports.SALES_TYPE = {
    SALES_PERSON: "SALES_PERSON",
    ALL: "ALL"
}

exports.SALE_DURATION_TYPE = {
    DAY: "DAY",
    WEEK: "WEEK",
    MONTH: "MONTH"
}

exports.DEAL_TYPE = {
    DISCOUNT: "DISCOUNT",
    BULK_PRICING: "BULK_PRICING",
    BUY_X_AND_GET_Y: "BUY_X_AND_GET_Y"
}

exports.DISCOUNT_TYPE = {
    PERCENT: "PERCENT",
    AMOUNT: "AMOUNT",
}

exports.DEAL_STATUS = {
    PROGRESS: "PROGRESS",
    RUNNING: "RUNNING",
    ENDED: "ENDED",
    SCHEDULED: "SCHEDULED",
    CANCELLED: "CANCELLED",
    PAUSED: "PAUSED",
    ARCHIVED: "ARCHIVED",
};

exports.DEAL_SORT_TYPE = {
    MANUAL: "MANUAL",
    PRICE: "PRICE",
    INVENTORY: "INVENTORY",
    // POPULARITY: "POPULARITY"
};

exports.BOOLEAN = {
    TRUE: "TRUE",
    FALSE: "FALSE",
};

exports.PERMISSION_MODULES = {
    CUSTOMERS: "customers",
    DASHBOARD: "dashboard",
    ORDERS: "orders",
    TRACKING: "tracking",
    PAYMENTS: "payments",
    PRODUCTS: "products",
    INVENTORY: "inventory",
    CATEGORIES: "categories",
    USERS: "users",
    IMAGES: "images",
    DEALS: "deals",
    COLLECTIONS: "collections",

    DATA_SHEET: "datasheet",
    DATA_SHEET_PRODUCT: "datasheet_product",
    DATA_SHEET_PRICE: "datasheet_price",
    DATA_SHEET_INVENTORY: "datasheet_inventory",
    DATA_SHEET_CUSTOMER: "datasheet_customer",
    DATA_SHEET_ORDERS: "datasheet_orders",

    REWARD_PROGRAM: "reward_program",
    REWARD_PROGRAM_MEMBER: "reward_program_member",
    REWARD_PROGRAM_POINT: "reward_program_point",
    REWARD_PROGRAM_PRODUCT: "reward_program_product",
    REWARD_PROGRAM_PRODUCT_CLAIM: "reward_program_product_claim",

    SETTINGS_MASTER_DATA: "settings_master_data",
    SETTINGS_MASTER_DATA_ATTRIBUTE: "settings_master_data_attribute",
    SETTINGS_MASTER_DATA_ATTRIBUTE_SET: "settings_master_data_attribute_set",
    SETTINGS_MASTER_DATA_PRICE_LIST: "settings_master_data_price_list",
    SETTINGS_MASTER_DATA_BRAND: "settings_master_data_brand",
    SETTINGS_MASTER_DATA_UOM: "settings_master_data_uom",

    SETTINGS_CONFIGURATIONS: "settings_configurations",
    SETTINGS_CONFIGURATIONS_ACCOUNT_INFO: "settings_configurations_account_info",
    SETTINGS_CONFIGURATIONS_SHIPPING_LABEL: "settings_configurations_shipping_label",
    SETTINGS_CONFIGURATIONS_APP_SETTING: "settings_configurations_app_setting",
    SETTINGS_CONFIGURATIONS_TAX_SETTING: "settings_configurations_tax_setting",
    SETTINGS_CONFIGURATIONS_TAX: "settings_configurations_tax",
    SETTINGS_CONFIGURATIONS_TRACKING: "settings_configurations_tracking",
    SETTINGS_CONFIGURATIONS_REWARD_PROGRAM: "settings_configurations_reward_program",
}

exports.ACTIONS = {
    VIEW: "view",
    EDIT: "edit",
    DELETE: "delete",
    CREATE: "create",
}

exports.HOLD_REASON_TEMPLATES = {
    TYPE: {
        SALES_PERSON: "sales_person",
        CUSTOMER: "customer",
    },
    OPTIONS_TYPE: {
        MAP: 'MAP',
        INPUT_FIELD: 'INPUT_FIELD'
    },
    OPTIONS: [
        {
            title: "Legal Name",
            type: "text",
            key: "legal_name"
        },
        {
            title: "Salesperson Name",
            type: "text",
            key: "sales_person_name"
        },
        {
            title: "Input Field",
            type: "input_field",
            key: "input_field"
        }
    ]
}

exports.MESSAGE_BIRD_API_DYNAMIC_ID = {
    WORKSPACE_ID: '{workspaceId}',
    PROJECT_ID: '{projectId}',
    TEMPLATE_ID: '{channelTemplateId}',
    CHANNEL_ID: '{channelId}',
    MESSAGE_ID: '{messageId}'
};

exports.MESSAGE_BIRD = {
    API_END_POINT: {
        PROJECTS: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/projects`,
        PROJECTS_BY_ID: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/projects/${this.MESSAGE_BIRD_API_DYNAMIC_ID.PROJECT_ID}`,
        PROJECT_TEMPLATE_DETAILS: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/projects/${this.MESSAGE_BIRD_API_DYNAMIC_ID.PROJECT_ID}/channel-templates/${this.MESSAGE_BIRD_API_DYNAMIC_ID.TEMPLATE_ID}`,
        SEND_MESSAGE: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/channels/${this.MESSAGE_BIRD_API_DYNAMIC_ID.CHANNEL_ID}/messages`,
        MESSAGE_STATUS: `workspaces/${this.MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID}/channels/${this.MESSAGE_BIRD_API_DYNAMIC_ID.CHANNEL_ID}/messages/{messageId}`
    },
    PAGINATION_LIMIT: 40
}

exports.MESSAGE_REQUEST = {
    "sender": {
        "connector": {
            "identifierValue": 'string',
            "annotations": {
                "name": "string"
            }
        }
    },
    "receiver": {
        "contacts": [
            {
                "identifierKey": "string",
                "identifierValue": "string"
            }
        ]
    },
    "template": {
        "projectId": "",
        "version": "string",
        "locale": "string",
        "variables": {
        }
    }
};

exports.NOTIFICATION = {
    TYPE: {
        NEW_PRODUCTS: "NEW_PRODUCTS",
        NEW_ORDER: "NEW_ORDER",
        ORDER_SHIPPED: "ORDER_SHIPPED",
        ORDER_PREPARING: "ORDER_PREPARING",
        ORDER_DELIVERED: "ORDER_DELIVERED",
        DEALS_UPDATES: "DEALS_UPDATES",
    }
}

exports.SAP_SERVICE = {
    CREDENTIALS: {
        URL: process.env.SAP_SERVICE_URL,
        USER_NAME: process.env.SAP_SERVICE_USER_NAME,
        PASSWORD: process.env.SAP_SERVICE_PASSWORD,
    },
    DOCUMENT_TYPE: {
        INVOICE: "IN",
        PAYMENT: "RC",
        CREDIT_NOTES: "CN",
        JOURNAL_ENTRY: "JE"
    },
}

exports.INTEGRATION_CHANNELS = {
    MESSAGE_BIRD: "MESSAGE_BIRD",
    SAP_SERVICE: "SAP_SERVICE",
}

exports.MONGODB = {
    OPERATION_TYPE: {
        INSERT: "insert",
        UPDATE: "update",
        DELETE: "delete",
    }
}

exports.PROMISE_STATES = {
    PENDING: "pending",
    FULFILLED: "fulfilled",
    REJECTED: "rejected",
}

exports.CART_ITEM_OPERATION = {
    CREATE: "create",
    UPDATE: "update",
    DELETE: "delete",
}

exports.DB_OPERATION = {
    CREATE: "create",
    UPDATE: "update",
    DELETE: "delete",
    REPLACE: "replace",
    UPSERT: "upsert",
}

