const DataSyncService = require("../Services/data_sync/DataSyncService");

class DataSyncController {

    async getMasterData(req, res) {
        try {
            const response = await DataSyncService.getMasterData(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCategoryList(req, res) {
        try {
            const response = await DataSyncService.getCategoryList(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getMvProductList(req, res) {
        try {
            const response = await DataSyncService.getMvProductList(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getProductList(req, res) {
        try {
            const response = await DataSyncService.getProductList(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getDeletedProductList(req, res) {
        try {
            const response = await DataSyncService.getDeletedProductList(req.body);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getImageList(req, res) {
        try {
            const response = await DataSyncService.getImageList(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getDeletedImageList(req, res) {
        try {
            const response = await DataSyncService.getDeletedImageList(req.body);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getFavoriteProductList(req, res) {
        try {
            const response = await DataSyncService.getFavoriteProductList(req);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getDealList(req, res) {
        try {
            const response = await DataSyncService.getDealList(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getDealProductList(req, res) {
        try {
            const response = await DataSyncService.getDealProductList(req.body);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCartItemList(req, res) {
        try {
            const response = await DataSyncService.getCartItemList(req);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getOrderList(req, res) {
        try {
            const response = await DataSyncService.getOrderList(req);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async cartItemOperation(req, res) {
        try {
            const response = await DataSyncService.cartItemOperation(req.body);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async syncCartItemsForCustomer(req, res) {
        try {
            const response = await DataSyncService.syncCartItemsForCustomer(req.body);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }
}

module.exports = new DataSyncController();
