const axios = require('axios').default
const { validationResult } = require('express-validator')

const sendFailureEmail = require('./SendFailureEmail').default
const { VALUES } = require('../Configs/constants')

exports.authentication = async (request, response, next) => {
    const errors = validationResult(request).formatWith(({ msg }) => msg)

    if (!errors.isEmpty()) {
        sendFailureEmail(request, errors.array())
            .catch(error => {
                logger.error(error)
            })

        return response.handler.badRequest(
            undefined,
            errors.array()
        );
    }

    // USED FOR WHEN AUTHENTICATION IS OPTIONAL
    if (!request.headers.authorization)
        return next()

    response.setHeader("Cache-Control", "no-cache");
    try {
        const configs = {
            headers: {
                "authorization": request.headers.authorization,
                "devicetoken": request.headers.devicetoken,
                "refreshToken": request.headers.refreshtoken,
                "userroleid": request.headers.userroleid,
            }
        }

        const deviceAccessType = request.headers.deviceaccesstype
        const deviceType = request.headers.devicetype

        if (deviceAccessType) {
            configs["headers"]["deviceaccesstype"] = deviceAccessType
        }

        if (deviceType) {
            configs["headers"]["deviceType"] = deviceType
        }

        if (request.originalUrl.includes('/dataSync')) {
            configs["headers"]["dataSync"] = "true"
        }

        const authResponse = await axios.get(VALUES.userServiceBaseURL + "auth/service-authenticator", configs)
        const refreshedAccessToken = authResponse?.["headers"]?.["refreshed-access-token"]
        const userDetails = authResponse?.["data"]?.["data"]?.["userDetails"]
        const userRoleDetails = authResponse?.["data"]?.["data"]?.["userRoleDetails"]
        const tenantDetail = authResponse?.["data"]?.["data"]?.["tenantDetail"]

        if (refreshedAccessToken) {
            response.setHeader("refreshed-access-token", refreshedAccessToken)
        }

        if (userDetails) {
            request.headers.userDetails = userDetails
            request.headers.userRoleDetails = userRoleDetails
            request.headers.tenantDetail = tenantDetail
        }

        return next()
    }
    catch (error) {
        const errorCode = error.response?.status || STATUS_CODES.SERVER_ERROR
        const errorMessage = error.response?.data?.message || error.message

        return response.handler.custom(
            errorCode,
            errorMessage,
            null,
            error
        )
    }
}
