const { body } = require('express-validator');

const {
    headerValidator,

    tenantIdQueryValidator,
    tenantIdBodyValidator,

    lastSyncedAtQueryValidator,
    lastSyncedAtBodyValidator,

    cursorMongoIdQueryValidator,
    cursorMongoIdBodyValidator,

    isInitialSyncQueryValidator,

    perPageValidator,
    perPageBodyValidator,
} = require('./CommonValidator');

exports.getMasterData = [
    ...headerValidator,
    ...tenantIdQueryValidator,
];

exports.getDataValidator = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...lastSyncedAtQueryValidator,
    ...cursorMongoIdQueryValidator,
    ...perPageValidator,
];

exports.getBodyDataValidator = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...lastSyncedAtBodyValidator,
    ...cursorMongoIdBodyValidator,
    ...perPageBodyValidator,
];

exports.getCommonDataValidator = [
    ...this.getDataValidator,
    ...isInitialSyncQueryValidator,
];

exports.getDealProductListValidator = [
    ...this.getBodyDataValidator,

    body("dealIds", "Please provide valid 'dealIds'(i.e.min 1) in array")
        .isArray({ min: 1 }),

    body("dealIds.*", "Please provide valid 'dealIds'")
        .isMongoId()
        .withMessage("Please provide valid 'dealIds' as MongoDB ObjectId"),
];

exports.cartItemOperation = [
    ...headerValidator,
];

exports.syncCartItemsForCustomer = [
    ...headerValidator,
    
    body("tenantId", "Please provide valid 'tenantId'")
        .isInt({ min: 1 })
        .withMessage("Please provide valid 'tenantId' as a positive integer"),
    
    body("customerUserRoleId", "Please provide valid 'customerUserRoleId'")
        .isMongoId()
        .withMessage("Please provide valid 'customerUserRoleId' as MongoDB ObjectId"),
    
    body("cartId", "Please provide valid 'cartId'")
        .isString()
        .notEmpty()
        .withMessage("Please provide valid 'cartId'")
];
