const express = require("express")
const router = express.Router()

const AuthenticateUser = require("../Middleware/authentication").authentication
const verifyPermission = require("../Middleware/verifyPermission").verifyPermission

const DataSyncValidator = require("../Middleware/validators/DataSyncValidator")
const DataSyncController = require("../Controllers/DataSyncController")

const {
    PERMISSION_MODULES,
    ACTIONS
} = require("../Configs/constants")

router.route("/masterData")
    .get(
        DataSyncValidator.getMasterData,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_PRICE_LIST]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_MASTER_DATA_UOM]: ACTIONS.VIEW,

            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TAX_SETTING]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TAX]: ACTIONS.VIEW,
        }),
        DataSyncController.getMasterData
    )

router.route("/categories")
    .get(
        DataSyncValidator.getCommonDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.CATEGORIES]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
        }),
        DataSyncController.getCategoryList
    )

router.route("/mvproducts")
    .get(
        DataSyncValidator.getCommonDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        DataSyncController.getMvProductList
    )

router.route("/products")
    .get(
        DataSyncValidator.getCommonDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        DataSyncController.getProductList
    )
    .post(
        DataSyncValidator.getBodyDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        DataSyncController.getDeletedProductList
    )

router.route("/images")
    .get(
        DataSyncValidator.getDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.VIEW,
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
        }),
        DataSyncController.getImageList
    )
    .post(
        DataSyncValidator.getBodyDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.IMAGES]: ACTIONS.VIEW,
        }),
        DataSyncController.getDeletedImageList
    )

router.route("/favoriteProducts")
    .get(
        DataSyncValidator.getDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW
        }),
        DataSyncController.getFavoriteProductList
    )

router.route("/deals")
    .get(
        DataSyncValidator.getDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
        }),
        DataSyncController.getDealList
    )

router.route("/dealProducts")
    .post(
        DataSyncValidator.getDealProductListValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.DEALS]: ACTIONS.VIEW
        }),
        DataSyncController.getDealProductList
    )

router.route("/cartItems")
    .post(
        DataSyncValidator.cartItemOperation,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: [
                ACTIONS.CREATE,
                ACTIONS.VIEW,
                ACTIONS.EDIT,
                ACTIONS.DELETE
            ]
        }),
        DataSyncController.cartItemOperation
    )
    .get(
        DataSyncValidator.getDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        DataSyncController.getCartItemList
    )

router.route("/cartItems/customerSync")
    .post(
        DataSyncValidator.syncCartItemsForCustomer,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: [
                ACTIONS.CREATE,
                ACTIONS.VIEW,
                ACTIONS.EDIT,
                ACTIONS.DELETE
            ]
        }),
        DataSyncController.syncCartItemsForCustomer
    )

router.route("/orders")
    .get(
        DataSyncValidator.getDataValidator,
        AuthenticateUser,
        verifyPermission({
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        DataSyncController.getOrderList
    )

module.exports = router
