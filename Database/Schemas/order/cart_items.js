const mongoose = require("mongoose");
const { CART_ACTIONS } = require("../../../Configs/constants");
const Schema = mongoose.Schema;
const { TaxCalculationSchema, ProductDealInfoSchema } = require("./common");

const CartItemSchema = new Schema({
    last_cart_action: {
        type: String,
        enum: Object.values(CART_ACTIONS),
        required: true
    },
    cart_id: {
        type: String,
        // ref: "carts_2.0",
        required: true,
        index: true
    },
    tenant_id: {
        type: Number,
        required: true,
        index: true
    },
    product_variant_id: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: "products_2.0",
        index: true
    },
    variant_id: {
        type: String,
    },
    item_number: { // product_item_number (previous)
        type: String,
        required: true
    },
    parent_item_number: { // variant_item_number (previous)
        type: String,
    },
    product_name: {
        type: String,
        required: true
    },
    product_secondary_name: {
        type: String
    },
    variant_name: {
        type: String,
    },
    variant_secondary_name: {
        type: String,
    },
    group_name: {
        type: String,
    },
    group_secondary_name: {
        type: String,
    },
    master_price_id: {
        type: Schema.Types.ObjectId,
        ref: "master_prices",
        required: true
    },
    base_price: {
        type: Number,
        required: true
    },
    tax: {
        type: Number,
        required: true,
        default: 0,
    },
    quantity: {
        type: Number,
        required: true,
    },
    min_qty: {
        type: Number,
        required: true,
    },
    uom_id: {
        type: Schema.Types.ObjectId,
        ref: "master_units",
        required: true,
    },
    uom_name: {
        type: String,
        required: true
    },
    item_comment: {
        type: [String]
    },
    customer_user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    sales_user_role_id: {
        type: Schema.Types.ObjectId,
        required: true,
    },
    tax_calculation_info: {
        type: TaxCalculationSchema
    },
    original_price: { // for storing display price
        type: Number
    },
    created_by: {
        type: Schema.Types.ObjectId,
    },
    update_by: {
        type: Schema.Types.ObjectId,
    },
    deal_info: {
        type: ProductDealInfoSchema
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

// Compound index for efficient querying - optimizes cart_id filter with created_at sort
CartItemSchema.index(
    { cart_id: 1, created_at: -1 },
    { name: "cart_id_created_at_desc" }
);

// Optimized index for complex queries with $or conditions and sorting
// This index prioritizes the most selective filters and supports the exact sort order
CartItemSchema.index(
    { tenant_id: 1, cart_id: 1, updated_at: 1, _id: 1 },
    { name: "tenant_cart_updated_id_optimized" }
);

// Alternative index for better regex performance when cart_id is very selective
CartItemSchema.index(
    { cart_id: 1, tenant_id: 1, updated_at: 1, _id: 1 },
    { name: "cart_tenant_updated_id_regex_first" }
);

// Specialized index for $or queries with updated_at and _id conditions
// This index is designed to handle the complex $or logic more efficiently
CartItemSchema.index(
    { tenant_id: 1, cart_id: 1, _id: 1, updated_at: 1 },
    { name: "tenant_cart_id_updated_or_optimized" }
);

// Text index for cart_id to support regex searches efficiently
// This is particularly useful for anchored regex patterns like /^pattern$/
CartItemSchema.index(
    { cart_id: "text" },
    { name: "cart_id_text_search" }
);

// Optimized index for syncCartItemsForCustomer queries
CartItemSchema.index(
    { tenant_id: 1, cart_id: 1, customer_user_role_id: 1, _id: 1 },
    { name: "sync_cart_items_optimized" }
);

// Additional performance index for the exact query pattern with updated_at
CartItemSchema.index(
    { tenant_id: 1, cart_id: 1, customer_user_role_id: 1, updated_at: 1, _id: 1 },
    {
        name: "sync_cart_items_performance_optimized",
        background: true,
        partialFilterExpression: {
            tenant_id: { $exists: true },
            cart_id: { $exists: true },
            customer_user_role_id: { $exists: true }
        }
    }
);

module.exports = mongoose.model("cart_items_2.0", CartItemSchema);
