const ConfigurationModel = new (require("../../Models/ConfigurationModel"))();
const MasterDataModel = new (require("../../Models/MasterDataModel"))();
const CategoryModel = new (require("../../Models/CategoryModel"))();
const ImageModel = new (require("../../Models/ImageModel"))();
const DeletedImageModel = require("../../Models/DeletedImageModel");
const ProductModel = new (require("../../Models/ProductModel"))();
const DealModel = new (require("../../Models/DealModel"))();
const DealProductModel = new (require("../../Models/DealProductModel"))();
const OrderModel = new (require("../../Models/OrderModel"))();
const MaterializedProductModelMethods = new (require("../../Models/materialized_product/MaterializedProductModelMethods"))();
const DeletedMaterializedProductModel = require("../../Models/materialized_product/DeletedMaterializedProductModel");

const BaseDataSyncService = require('./BaseDataSyncService');

const {
    toLeanOption,
} = require('../../Utils/helpers');

const { VALUES, DEAL_STATUS, CART_ITEM_OPERATION, DB_OPERATION } = require('../../Configs/constants');

class DataSyncService extends BaseDataSyncService {

    async getMasterData(query) {
        const {
            tenantId,
        } = query;

        const filter = { tenant_id: tenantId };

        const [
            taxSetting,
            taxTypes,
            masterPrices,
            masterUnits,
        ] = await Promise.all([
            ConfigurationModel.findMasterTaxInfo(filter, "-__v -created_by -updated_by", toLeanOption),
            ConfigurationModel.findTaxes(
                { tenant_id: tenantId },
                "-__v -created_by -updated_by",
                toLeanOption,
            ),
            MasterDataModel.findMasterPriceList(
                {
                    tenant_id: tenantId,
                    is_deleted: false,
                    is_active: true,
                },
                "-__v -created_by -updated_by",
                toLeanOption,
            ),
            MasterDataModel.findUnitList(
                {
                    tenant_id: tenantId,
                    is_deleted: false,
                    is_active: true,
                },
                "-__v -created_by -updated_by",
                toLeanOption,
            ),
        ]);

        const activeTaxes = []
        const taxGroupIds = []
        let taxGroups = []

        taxTypes.forEach(tax => {
            if (tax.status) {
                activeTaxes.push(tax)

                if (tax.type === VALUES.TAX_TYPE.GROUP) {
                    taxGroupIds.push(tax._id)
                }
            }
        });

        if (taxGroupIds.length > 0) {
            taxGroups = await ConfigurationModel.findTaxes(
                {
                    tenant_id: tenantId,
                    group_id: { $in: taxGroupIds },
                },
                "-__v -created_by -updated_by",
                toLeanOption
            );
        }

        return {
            taxSetting,
            taxTypes: activeTaxes,
            taxGroups,
            masterPrices,
            masterUnits,
        };
    }

    async getCategoryList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            isInitialSync,
            perPage = 50,
        } = query;

        const filter = { tenant_id: tenantId };

        // Only apply is_deleted and is_active filters for initial sync
        /*if (isInitialSync) {
            filter.is_deleted = false;
            filter.is_active = true;
        }*/

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => CategoryModel.getCategoryCount(filter),
            findFunction: (filter, selectFields, options) => CategoryModel.allCategory(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by",
            leanOptions: toLeanOption
        });
    }

    async getMvProductList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
            isInitialSync,
        } = query;

        const filter = { tenant_id: tenantId };

        // Only apply is_deleted and is_active filters for initial sync
        if (isInitialSync) {
            filter.is_active = true;
        }

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => MaterializedProductModelMethods.countProducts(filter),
            findFunction: (filter, selectFields, options) => MaterializedProductModelMethods.findProducts(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by",
            leanOptions: toLeanOption
        });
    }

    async getProductList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = query;

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => ProductModel.countProducts(filter),
            findFunction: (filter, selectFields, options) => ProductModel.findProducts(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by -inactive_variant_item_numbers",
            leanOptions: {
                ...toLeanOption,
                populate: [
                    { path: "variants.values", select: { name: 1, order: 1 , tenant_id: 1 ,product_id: 1 ,type: 1   } },
                    { path: "groups.values", select: { name: 1, order: 1  ,  tenant_id: 1 ,product_id: 1 ,type: 1 } },
                    { path: "attributes.attribute_id", select: { attribute_name: 1, secondary_language_attribute_name: 1, is_active: 1  ,tenant_id: 1 ,is_deleted: 1 ,created_at: 1 ,updated_at: 1   } },
                ]
            }
        });
    }

    async getDeletedProductList(body) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = body;

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => DeletedMaterializedProductModel.getDeletedMaterializedProductCount(filter),
            findFunction: (filter, selectFields, options) => DeletedMaterializedProductModel.findDeletedMaterializedProducts(filter, selectFields, options),
            selectFields: "-__v",
            leanOptions: toLeanOption,
        });
    }


    async getImageList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = query;

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => ImageModel.getImageCount(filter),
            findFunction: (filter, selectFields, options) => ImageModel.getImages(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by -image_size",
            leanOptions: toLeanOption
        });
    }

    async getDeletedImageList(body) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = body;

        const filter = { tenant_id: tenantId };

        // Special handling for lastSyncedAt without cursor - only for this API
        if (lastSyncedAt && !cursor) {
            logger.info('lastSyncedAt without cursor');
            const lastSyncedDate = new Date(lastSyncedAt).toISOString();
            const dateFilter = { updated_at: { $gte: lastSyncedDate } };
            const finalFilter = { ...filter, ...dateFilter };

            // Execute queries in parallel
            const [totalCount, records] = await Promise.all([
                DeletedImageModel.getDeletedImageCount(finalFilter),
                DeletedImageModel.findDeletedImages(
                    finalFilter,
                    "-__v",
                    {
                        ...toLeanOption,
                        sort: { updated_at: 1, _id: 1 },
                        limit: perPage,
                    }
                )
            ]);

            return {
                count: records.length === 0 ? 0 : totalCount,
                list: records,
            };
        }

        // Use standard executeDataSyncQuery for other cases
        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => DeletedImageModel.getDeletedImageCount(filter),
            findFunction: (filter, selectFields, options) => DeletedImageModel.findDeletedImages(filter, selectFields, options),
            selectFields: "-__v",
            leanOptions: toLeanOption,
        });
    }

    async getFavoriteProductList(req) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = req.query;

        const filter = {
            tenant_id: tenantId,
            user_role_id: req.headers.userroleid
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => ProductModel.getFavoriteProductCount(filter),
            findFunction: (filter, selectFields, options) => ProductModel.findFavoriteProducts(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by",
            leanOptions: toLeanOption
        });
    }

    async getDealList(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = query;

        const filter = {
            tenant_id: tenantId,
            deal_status: { $in: [DEAL_STATUS.RUNNING, DEAL_STATUS.SCHEDULED] }
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => DealModel.getDealCount(filter),
            findFunction: (filter, selectFields, options) => DealModel.getDeals(filter, selectFields, options),
            selectFields: `
                deal_id
                tenant_id
                price_id
                deal_type
                deal_name
                secondary_deal_name
                deal_from_date
                deal_to_date
                sales_persons
                deal_status
                deal_product_counter
                created_at
                updated_at
            `,
            leanOptions: toLeanOption
        });
    }

    async getDealProductList(body) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
            dealIds,
        } = body;

        const filter = {
            tenant_id: tenantId,
            is_active: true,
            deal_id: { $in: dealIds }
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => DealProductModel.getDealProductCount(filter),
            findFunction: (filter, selectFields, options) => DealProductModel.getDealProducts(filter, selectFields, options),
            selectFields: `
                deal_id
                price_id
                tenant_id
                product_id
                parent_id
                is_active
                discount_type
                percent
                amount
                discounted_price
                first_tier
                second_tier
                third_tier
                buy_product
                free_product
                deal_product_sequence
                deal_from_date
                deal_to_date
                created_at
                updated_at
            `,
            leanOptions: toLeanOption
        });
    }

    async getCartItemList(req) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = req.query;

        const filter = {
            tenant_id: tenantId,
            cart_id: new RegExp(`^${tenantId}_[^_]+_${req.headers.userroleid}$`),
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => OrderModel.cartItemCount(filter),
            findFunction: (filter, selectFields, options) => OrderModel.findCartItems(filter, selectFields, options),
            selectFields: "-__v -created_by -updated_by",
            leanOptions: toLeanOption
        });
    }

    async getOrderList(req) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = req.query;

        const filter = {
            tenant_id: tenantId,
            order_creator_user_role_id: req.headers.userroleid,
            created_at: { $gte: moment().subtract(3, "months") }
        };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => OrderModel.countOrders(filter),
            findFunction: (filter, selectFields, options) => OrderModel.findOrders(filter, selectFields, options),
            selectFields: `
                -__v
                -created_by
                -updated_by
                -master_price_ids
                -unique_order_number
                -order_hold_reason_id
                -whatsapp_message_sent
                -pre_approved
                -gps_coordinate
            `,
            leanOptions: toLeanOption
        });
    }
    async cartItemOperation(body) {
        const startTime = Date.now();
        if (!body) {
            return {
                processed: 0,
                skipped: 0,
                created: 0,
                updated: 0,
                deleted: 0,
                errors: ['No request body provided']
            };
        }

        const cartItems = body;

        if (!Array.isArray(cartItems) || cartItems.length === 0) {
            return {
                processed: 0,
                skipped: 0,
                created: 0,
                updated: 0,
                deleted: 0,
                errors: ['No cart items provided or invalid format']
            };
        }

        const requiredFields = [
            '_id', 'tenant_id', 'customer_user_role_id', 'sales_user_role_id',
            'product_variant_id', 'quantity', 'last_cart_action', 'item_number',
            'product_name', 'master_price_id', 'base_price', 'tax',
            'uom_id', 'uom_name', 'min_qty', 'action'
        ];

        const results = {
            processed: 0,
            skipped: 0,
            created: 0,
            updated: 0,
            deleted: 0,
            errors: [],
            validationDetails: {
                totalItems: cartItems.length,
                validItems: 0,
                invalidItems: 0,
                missingFieldErrors: [],
                invalidFormatErrors: []
            }
        };

        try {
            const validItems = this.validateCartItems(cartItems, requiredFields, results);
            if (validItems.length === 0) {
                logger.info('No valid cart items to process');
                
                // Add summary error messages
                if (results.validationDetails.missingFieldErrors.length > 0) {
                    results.errors.push(`Validation failed: ${results.validationDetails.missingFieldErrors.length} items have missing required fields`);
                }
                if (results.validationDetails.invalidFormatErrors.length > 0) {
                    results.errors.push(`Format errors: ${results.validationDetails.invalidFormatErrors.length} items have invalid format`);
                }
                
                return results;
            }

            const CHUNK_SIZE = 100;
            const itemChunks = validItems.length > CHUNK_SIZE
                ? this.chunkArray(validItems, CHUNK_SIZE)
                : [validItems];

            for (const [index, chunk] of itemChunks.entries()) {
                try {
                    logger.info(`Processing chunk ${index + 1} of ${itemChunks.length}`);

                    const existingItemsMap = await this.fetchExistingCartItems(chunk);
                    const operationGroups = this.groupOperationsByDbOperation(chunk, existingItemsMap, results);

                    if (!operationGroups) {
                        logger.info(`No operations to perform for chunk ${index + 1}`);
                        continue;
                    }

                    logger.info('operationGroups: ', {
                        ...Object.fromEntries(
                            Object.entries(operationGroups).map(([op, items]) => [op, items.length])
                        )
                    });

                    await this.executeBatchOperations(operationGroups, results);
                } catch (chunkError) {
                    logger.error(`Error processing chunk ${index + 1}:`, chunkError);
                    results.errors.push(`Chunk ${index + 1} failed: ${chunkError.message}`);
                }
            }

            logger.info('Cart items processing completed:', {
                totalItems: cartItems.length,
                validItems: validItems.length,
                ...results
            });
            const executionTime = Date.now() - startTime;
            logger.info(`Total execution time: ${executionTime}ms`);
            return results;

        } catch (error) {
            logger.error('Error in cartItemOperation:', error);
            results.errors.push(`System error: ${error.message}`);
            return results;
        }
    }


    validateCartItems(cartItems, requiredFields, results) {
        const validItems = [];

        // Additional safety check
        if (!Array.isArray(cartItems) || cartItems.length === 0) {
            return validItems;
        }

        if (!Array.isArray(requiredFields) || requiredFields.length === 0) {
            return validItems;
        }

        for (const cartItem of cartItems) {
            // Safety check for cartItem
            if (!cartItem || typeof cartItem !== 'object') {
                results.skipped++;
                results.validationDetails.invalidItems++;
                results.validationDetails.invalidFormatErrors.push({
                    itemId: cartItem?._id || 'unknown',
                    itemNumber: cartItem?.item_number || 'unknown',
                    action: cartItem?.action || 'unknown',
                    product_variant_id: cartItem?.product_variant_id || 'unknown',
                    error: 'Invalid cart item format - not an object',
                    //receivedValue: cartItem
                });
                continue;
            }

            // For DELETE operations, only validate _id and action fields
            if (cartItem.action === CART_ITEM_OPERATION.DELETE) {
                const deleteRequiredFields = ['_id', 'action'];
                const missingDeleteFields = deleteRequiredFields.filter(field => {
                    const value = cartItem[field];
                    return value === undefined || value === null || value === '';
                });

                if (missingDeleteFields.length > 0) {
                    logger.info(`Skipping DELETE cart item due to missing required fields: ${missingDeleteFields.join(', ')}`, {
                        cartItemId: cartItem._id,
                        missingFields: missingDeleteFields
                    });
                    
                    results.skipped++;
                    results.validationDetails.invalidItems++;
                    
                    results.validationDetails.missingFieldErrors.push({
                        itemId: cartItem._id || 'unknown',
                        action: cartItem?.action || 'unknown',
                        missingFields: missingDeleteFields,
                        operation: 'DELETE'
                    });
                    
                    continue;
                }

                validItems.push(cartItem);
                results.validationDetails.validItems++;
                continue;
            }

            // For non-DELETE operations, validate all required fields
            // Skip action field validation as it's added externally
            const missingFields = requiredFields.filter(field => {
                if (field === 'action') return false;
                const value = cartItem[field];
                return value === undefined || value === null || value === '';
            });

            if (missingFields.length > 0) {
                logger.info(`Skipping cart item due to missing fields: ${missingFields.join(', ')}`, {
                    cartItemId: cartItem._id,
                    itemNumber: cartItem.item_number,
                    missingFields
                });
                
                results.skipped++;
                results.validationDetails.invalidItems++;
                
                // Add detailed missing field error
                results.validationDetails.missingFieldErrors.push({
                    itemId: cartItem._id || 'unknown',
                    itemNumber: cartItem.item_number || 'unknown',
                    action: cartItem.action || 'unknown',
                    product_variant_id: cartItem.product_variant_id || 'unknown',
                    missingFields: missingFields,
                    //receivedFields: Object.keys(cartItem),
                    //requiredFields: requiredFields.filter(field => field !== 'action'),
                    //itemData: cartItem
                });
                
                continue;
            }

            validItems.push(cartItem);
            results.validationDetails.validItems++;
        }

        return validItems;
    }

    async fetchExistingCartItems(validItems) {
        if (!Array.isArray(validItems) || validItems.length === 0) {
            return new Map();
        }

        try {
            const existingItems = await this.batchFindCartItems(validItems);

            // Create lookup map for O(1) access
            const existingItemsMap = new Map();
            if (Array.isArray(existingItems)) {
                existingItems.forEach(item => {
                    if (item) {
                        const key = this.createItemKey(item);
                        existingItemsMap.set(key, item);
                    }
                });
            }

            return existingItemsMap;
        } catch (error) {
            logger.error('Error fetching existing cart items:', error);
            throw error;
        }
    }

    groupOperationsByDbOperation(validItems, existingItemsMap, results) {
        const operationGroups = Object.fromEntries(
            Object.values(DB_OPERATION).map(op => [op, []])
        );

        const safeItemsMap = existingItemsMap instanceof Map ? existingItemsMap : new Map();

        for (const cartItem of validItems) {
            // Safety check for cartItem
            if (!cartItem || typeof cartItem !== 'object') {
                results.skipped++;
                continue;
            }

            try {
                const key = this.createItemKey(cartItem);
                const existingItem = safeItemsMap.get(key);
                const operation = this.determineOperation(cartItem, existingItem);

                if (operation.skip) {
                    logger.info(`Skipping cart item: ${operation.reason}`, {
                        cartItemId: cartItem._id,
                        itemNumber: cartItem.item_number,
                        reason: operation.reason
                    });
                    results.skipped++;
                    continue;
                }

                // Safety check for valid dbOperation
                if (!operationGroups.hasOwnProperty(operation.dbOperation)) {
                    logger.error(`Invalid dbOperation: ${operation.dbOperation}`, {
                        cartItemId: cartItem._id,
                        itemNumber: cartItem.item_number
                    });
                    results.errors.push({
                        cartItem: cartItem._id,
                        error: `Invalid dbOperation: ${operation.dbOperation}`
                    });
                    continue;
                }

                operationGroups[operation.dbOperation].push({
                    cartItem,
                    existingItem,
                    dbOperation: operation.dbOperation
                });

                results.processed++;
            } catch (error) {
                logger.error(`Error processing cart item:`, {
                    error: error.message,
                    cartItemId: cartItem._id,
                    itemNumber: cartItem.item_number,
                    stack: error.stack
                });
                results.errors.push({
                    cartItem: cartItem._id,
                    error: error.message
                });
            }
        }

        return operationGroups;
    }


    // Helper method to create consistent item keys
    createItemKey(item) {
        return `${item.tenant_id}-${item.sales_user_role_id}-${item.customer_user_role_id}-${item.product_variant_id}-${item.item_number}`;
    }

    async batchFindCartItems(items) {
        if (!Array.isArray(items) || items.length === 0) {
            return [];
        }

        try {
            const orConditions = items.map(item => ({
                $and: [
                    { cart_id: item.cart_id },
                    { tenant_id: item.tenant_id },
                    { sales_user_role_id: item.sales_user_role_id },
                    { customer_user_role_id: item.customer_user_role_id },
                    { product_variant_id: item.product_variant_id },
                    { item_number: item.item_number }
                ]
            }));

            const existingItems = await OrderModel.findCartItems(
                { $or: orConditions },
                {
                    _id: 1,
                    updated_at: 1,
                    created_at: 1,
                    tenant_id: 1,
                    sales_user_role_id: 1,
                    customer_user_role_id: 1,
                    product_variant_id: 1,
                    item_number: 1,
                    cart_id: 1
                }
            );

            const safeExistingItems = existingItems || [];
            logger.info(`Found ${safeExistingItems.length} existing cart items from ${items.length} items`);
            return safeExistingItems;

        } catch (error) {
            logger.error('Error in batchFindCartItems:', error);
            throw error;
        }
    }


    determineOperation(cartItem, existingItem) {
        const action = cartItem.action;

        switch (action) {
            case CART_ITEM_OPERATION.CREATE:
                if (existingItem) {
                    logger.info(`Found existing item for cart item: ${cartItem._id}`, {
                        cartItemId: cartItem._id,
                        itemNumber: cartItem.item_number,
                        existingItemId: existingItem._id
                    });
                    const existingTime = new Date(existingItem.updated_at).getTime();
                    const cartTime = new Date(cartItem.updated_at).getTime();
                    logger.info('Comparing updated_at timestamps:', {
                        existingTime,
                        cartTime,
                        existingDate: existingItem.updated_at,
                        cartDate: cartItem.updated_at
                    });

                    if (existingTime >= cartTime) {
                        logger.info(`Skipping cart item due to existing item having newer timestamp: ${cartItem._id}`, {
                            cartItemId: cartItem._id,
                            itemNumber: cartItem.item_number,
                            existingItemId: existingItem._id,
                            existingDate: existingItem.updated_at,
                            cartDate: cartItem.updated_at
                        });
                        return { skip: true, reason: 'Existing item has newer timestamp' };
                    }
                    return { action: CART_ITEM_OPERATION.CREATE, dbOperation: DB_OPERATION.REPLACE };
                }
                return { action: CART_ITEM_OPERATION.CREATE, dbOperation: DB_OPERATION.CREATE };

            case CART_ITEM_OPERATION.UPDATE:
                if (existingItem) {
                    const existingTime = new Date(existingItem.updated_at).getTime();
                    const cartTime = new Date(cartItem.updated_at).getTime();
                     logger.info(`Skipping cart item due to existing item having newer timestamp: ${cartItem._id}`, {
                            cartItemId: cartItem._id,
                            itemNumber: cartItem.item_number,
                            existingItemId: existingItem._id,
                            existingDate: existingItem.updated_at,
                            cartDate: cartItem.updated_at
                        });
                    if (existingTime >= cartTime) {
                        return { skip: true, reason: 'Existing item has newer timestamp' };
                    }
                    return { action: CART_ITEM_OPERATION.UPDATE, dbOperation: DB_OPERATION.UPSERT };
                }
                return { action: CART_ITEM_OPERATION.UPDATE, dbOperation: DB_OPERATION.UPSERT };

            case CART_ITEM_OPERATION.DELETE:
                return { action: CART_ITEM_OPERATION.DELETE, dbOperation: DB_OPERATION.DELETE };

            default:
                logger.warn(`Unknown cartItem.action encountered: ${action}`, {
                    cartItemId: cartItem._id,
                    itemNumber: cartItem.item_number
                });
                return { skip: true, reason: `Unknown action: ${action}` };
        }
    }


    async executeBatchOperations(operationGroups, results) {
        const batchSize = 100; // Increased batch size for better performance

        try {
            // Process each operation type separately for better error handling
            for (const [operationType, operations] of Object.entries(operationGroups)) {
                await this.processBatchOperations(operations, results, batchSize, operationType);
            }

        } catch (error) {
            logger.error('Error in batch operations execution:', error);
            throw error;
        }
    }

    async processBatchOperations(operations, results, batchSize, operationType) {
        if (!Array.isArray(operations) || operations.length === 0) return;

        const batches = this.chunkArray(operations, batchSize);

        for (const batch of batches) {
            // Safety check for batch
            if (!Array.isArray(batch) || batch.length === 0) continue;

            try {
                const bulkOps = [];

                for (const item of batch) {
                    // Safety check for item
                    if (!item || !item.cartItem) {
                        logger.warn('Invalid item in batch, skipping');
                        continue;
                    }

                    const { cartItem, dbOperation, existingItem } = item;
                    const { action, ...cleanedCartItem } = cartItem;

                    switch (dbOperation) {
                        case DB_OPERATION.CREATE:
                            bulkOps.push({
                                insertOne: {  // Changed from createOne to insertOne
                                    document: cleanedCartItem,
                                },
                            });
                            break;

                        case DB_OPERATION.REPLACE:
                            if (existingItem) {
                                bulkOps.push({
                                    deleteOne: {
                                        filter: { _id: existingItem._id },
                                    },
                                });
                            }
                            bulkOps.push({
                                insertOne: {  // Changed from createOne to insertOne
                                    document: cleanedCartItem,
                                },
                            });

                            break;

                        case DB_OPERATION.UPSERT:
                            bulkOps.push({
                                updateOne: {
                                    filter: { _id: cleanedCartItem._id },
                                    update: { $set: cleanedCartItem },
                                    upsert: true,
                                },
                            });
                            break;

                        case DB_OPERATION.DELETE:
                            bulkOps.push({
                                deleteOne: {
                                    filter: { _id: cleanedCartItem._id },
                                },
                            });
                            break;
                    }
                }

                if (bulkOps.length > 0) {
                    const result = await OrderModel.bulkWriteCartItems(bulkOps, { ordered: false  , timestamps : false });
                    logger.info(`Bulk operation result:`, result);

                    if (result) {
                        results.created += (result.insertedCount || 0) + (result.upsertedCount || 0);
                        results.updated += (result.modifiedCount || 0);
                        results.deleted += (result.deletedCount || 0);

                        logger.info(`Batch ${operationType} processed:`, {
                            batchSize: batch.length,
                            insertedCount: result.insertedCount || 0,
                            modifiedCount: result.modifiedCount || 0,
                            deletedCount: result.deletedCount || 0,
                            upsertedCount: result.upsertedCount || 0
                        });
                    }
                }

            } catch (error) {
                logger.error(`Error in batch ${operationType}:`, error);
                await this.handleBatchError(batch, results);
            }
        }
    }

    async handleBatchError(batch, results) {
        logger.warn(`Falling back to individual processing for failed batch operation`);

        for (const item of batch) {
            const { cartItem, dbOperation, existingItem } = item;
            const { action, ...cleanedCartItem } = cartItem;

            try {
                switch (dbOperation) {
                    case DB_OPERATION.CREATE:
                        await OrderModel.createCartItems(cleanedCartItem);
                        results.created++;
                        break;

                    case DB_OPERATION.REPLACE:
                        await OrderModel.deleteCartItems({ _id: existingItem._id });
                        await OrderModel.createCartItems(cleanedCartItem);
                        results.created++;
                        break;

                    case DB_OPERATION.UPSERT:
                        const upsertResult = await OrderModel.upsertCartItem(
                            { _id: cleanedCartItem._id },
                            cleanedCartItem,
                            { upsert: true, new: true }
                        );
                        // Check if it was an create or update
                        if (upsertResult.upserted) {
                            results.created++;
                        } else {
                            results.updated++;
                        }
                        break;

                    case DB_OPERATION.DELETE:
                        const deleteResult = await OrderModel.deleteCartItems({ _id: cleanedCartItem._id });
                        if (deleteResult.deletedCount > 0) {
                            results.deleted++;
                        }
                        break;
                }
                logger.info(`Individual ${dbOperation} successful for item: ${cleanedCartItem._id}`);
            } catch (error) {
                logger.error(`Error in individual ${dbOperation}:`, {
                    error: error.message,
                    cartItemId: cleanedCartItem._id,
                    itemNumber: cleanedCartItem.item_number
                });
                results.errors.push({
                    cartItem: cleanedCartItem._id,
                    error: error.message
                });
            }
        }
    }

    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    async syncCartItemsForCustomer(req) {
        const { tenantId, customerUserRoleId, cartId, cartItems } = req;

        try {
            logger.info(`Starting cart items analysis for cart: ${cartId}, items: ${cartItems ? cartItems.length : 0}`);
            
            // Use optimized analysis for all scenarios (including null/empty cartItems)
            return await this.analyzeOptimizedDataset(tenantId, customerUserRoleId, cartId, cartItems);
            
        } catch (error) {
            logger.error('Error in syncCartItemsForCustomer analysis:', error);
            throw error;
        }
    }

    async analyzeOptimizedDataset(tenantId, customerUserRoleId, cartId, cartItems) {
        const startTime = Date.now();
        logger.info(`Starting optimized analysis for ${cartItems ? cartItems.length : 0} cart items`);
        
        // Single optimized database query with minimal necessary fields and no projection
        const filter = {
            tenant_id: tenantId,
            cart_id: cartId,
            customer_user_role_id: customerUserRoleId
        };
        const dbStart = Date.now();
        let existingCartItems;
        try {
            existingCartItems = await OrderModel.findCartItems(
                filter,
                {},
                { lean: true }
            );
        } catch (hintError) {
            logger.warn("Index hint failed, retrying without hint", { error: hintError?.message });
            existingCartItems = await OrderModel.findCartItems(
                filter,
                {},
                { lean: true }
            );
        }
        const dbQueryMs = Date.now() - dbStart;
        logger.info("Cart sync DB fetch completed", { count: existingCartItems?.length || 0, dbQueryMs });

        // Pre-allocate arrays with estimated sizes for better memory management
        const createArray = [];
        const updateArray = [];
        const deleteArray = [];
        let skipCount = 0;

        // Handle null/empty cartItems case
        if (!cartItems || cartItems === null || cartItems.length === 0) {
            logger.info(`Cart items is null for cart: ${cartId}, getting existing items to create`);
            
            if (existingCartItems && existingCartItems.length > 0) {
                logger.info(`Found ${existingCartItems.length} existing cart items, sending to create`);

                createArray.push(...existingCartItems)

                return this.buildAnalysisResult([], existingCartItems.length, createArray, [], [], 0, "existing_cart_items");
               
            } else {
                logger.info(`No existing cart items found for cart: ${cartId}`);
                return this.buildAnalysisResult([], 0, [], [], [], 0, "null_cart_items");
            }
        }
        
        // Create optimized lookup structures using more efficient data structures
        const existingItemsMap = new Map(); // Single map for all data
        const requestItemsSet = new Set(); // For O(1) lookup

        // Build existing items lookup with minimal memory footprint
        for (const item of existingCartItems) {
            const itemId = item._id.toString();
            existingItemsMap.set(itemId, item);
        }

        // Build request items lookup
        for (const item of cartItems) {
            requestItemsSet.add(item._id.toString());
        }

        

        // Single-pass processing for better performance
        for (const cartItem of cartItems) {
            const cartItemId = cartItem._id.toString();
            const cartItemTime = new Date(cartItem.updated_at).getTime(); // Convert to timestamp
            const existingData = existingItemsMap.get(cartItemId);

            if (existingData) {
                // Item exists - check timestamps
                if (cartItemTime < existingData.updated_at) {
                    // DB is newer - UPDATE with request data
                    updateArray.push(existingData);
                } else {
                    // Request is newer or equal - SKIP
                    skipCount++;
                }
            }

            if (!existingItemsMap.has(cartItemId)) {
                deleteArray.push(

                    cartItem._id    
                );
            }
        }

        // Find items to create (items in DB but not in request)

        for (const existingItem of existingCartItems) {
            const itemId = existingItem._id.toString();
            if (!requestItemsSet.has(itemId)) {
                createArray.push(

                    existingItem
                );
            }
        }

        // Find items to delete (items in request but not in DB)

        const executionTime = Date.now() - startTime;
        const processingMs = executionTime - dbQueryMs;
        logger.info(`Optimized analysis completed`, { totalMs: executionTime, dbQueryMs, processingMs, create: createArray.length, update: updateArray.length, delete: deleteArray.length, skip: skipCount });
        
        return this.buildAnalysisResult(cartItems, existingCartItems.length, createArray, updateArray, deleteArray, skipCount, "optimized_dataset");
    }

    buildAnalysisResult(cartItems, totalExisting, createArray, updateArray, deleteArray, skipCount, optimization) {
        return {
            success: true,
            message: `Cart items analysis completed successfully (${optimization} optimization)`,
            analysis: {
                summary: {
                    totalRequested: cartItems.length,
                    totalExisting: totalExisting,
                    wouldCreate: createArray.length,
                    wouldUpdate: updateArray.length,
                    wouldDelete: deleteArray.length,
                    wouldSkip: skipCount,
                    optimization: optimization
                },
                details: {
                    createArray,
                    updateArray,
                    deleteArray,
                    skipArray: [] // Empty array since we only track count
                }
            }
        };
    }
}
module.exports = new DataSyncService();
